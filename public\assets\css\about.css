/* Variables */
:root {
    --primary-color: #00b5ad;
    --primary-dark: #009d96;
    --text-color: #2c3e50;
    --text-light: #666;
    --white: #fff;
    --light-gray: #f8f9fa;
    --border-radius: 12px;
    --box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* About us page specific styles */

/* Hero Section */
.hero-section {
    background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.4)), url('../img/home/<USER>');
    background-size: cover;
    background-position: center;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    position: relative;
    margin-bottom: 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.hero-content h1 {
    font-size: 56px;
    font-weight: 800;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.hero-content .lead {
    font-size: 20px;
    max-width: 700px;
    margin: 0 auto;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* About Section */
.about-section {
    padding: 100px 0;
    background-color: var(--white);
    position: relative;
}

.about-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><rect width="2" height="2" fill="%23f1f1f1" /></svg>');
    background-size: 30px 30px;
    opacity: 0.5;
    z-index: 0;
}

.about-content {
    max-width: 900px;
    margin: 0 auto 80px;
    position: relative;
    z-index: 1;
    background-color: var(--white);
    padding: 60px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.about-text {
    position: relative;
}

.about-text p {
    color: var(--text-color);
    font-size: 18px;
    line-height: 1.9;
    margin-bottom: 28px;
    padding-right: 15px;
    padding-left: 15px;
    position: relative;
}

.about-text p:first-child {
    font-size: 20px;
    font-weight: 500;
    color: var(--primary-dark);
    border-right: 3px solid var(--primary-color);
    padding-right: 20px;
}

.about-text p:last-child {
    margin-bottom: 0;
}

.about-text::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 80px;
    height: 80px;
    background-color: rgba(0, 181, 173, 0.1);
    border-radius: 50%;
    z-index: -1;
}

.about-text::after {
    content: '';
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 120px;
    height: 120px;
    background-color: rgba(0, 181, 173, 0.05);
    border-radius: 50%;
    z-index: -1;
}

/* Partners Section */
.partners-section {
    position: relative;
    z-index: 1;
    padding: 100px 0;
    text-align: center;
    background: linear-gradient(180deg, var(--white) 0%, var(--light-gray) 100%);
}

.partners-section h2 {
    font-size: 42px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 60px;
    position: relative;
    display: inline-block;
}

.partners-section h2::before {
    content: '';
    position: absolute;
    bottom: -15px;
    left: -20px;
    width: calc(100% + 40px);
    height: 4px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.partner-card {
    background-color: var(--white);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 20px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
}

.partner-card img {
    max-width: 90%;
    height: auto;
    margin: 0 auto;
    display: block;
}

/* Media Queries */
@media (max-width: 991px) {
    .hero-section {
        height: 400px;
    }

    .hero-content h1 {
        font-size: 48px;
    }

    .about-content {
        margin: 0 20px 60px;
    }

    .partners-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 30px;
    }

    .partner-card {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 300px;
    }

    .hero-content h1 {
        font-size: 36px;
    }

    .about-content {
        padding: 30px;
    }

    .about-text p {
        font-size: 16px;
        line-height: 1.8;
        margin-bottom: 20px;
    }

    .about-text p:first-child {
        font-size: 18px;
    }

    .partners-section h2 {
        font-size: 36px;
        margin-bottom: 40px;
    }
}

@media (max-width: 576px) {
    .hero-section {
        height: 250px;
    }

    .hero-content h1 {
        font-size: 32px;
    }

    .about-content {
        padding: 25px 20px;
    }

    .about-text p {
        font-size: 15px;
        padding-right: 10px;
        padding-left: 10px;
    }

    .about-text p:first-child {
        font-size: 17px;
        padding-right: 15px;
    }

    .partners-grid {
        grid-template-columns: 1fr;
        max-width: 320px;
        margin: 0 auto;
    }

    .partner-card {
        padding: 25px;
    }

    .partner-card img {
        max-width: 200px;
    }
}
