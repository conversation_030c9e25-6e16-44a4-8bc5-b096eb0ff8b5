<!DOCTYPE html>
<html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <title>@yield('title') | <span class="notranslate">K<PERSON>bara</span></title>

        <!-- Meta Tags -->
        @yield('meta')

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('assets/img/home/<USER>') }}" type="image/x-icon">

        <!-- CSS -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Main CSS -->
        <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}?t={{ time() }}">


        <!-- Parts CSS -->
        <link rel="stylesheet" href="{{ asset('assets/css/parts/top-bar.css') }}?t={{ time() }}">
        <link rel="stylesheet" href="{{ asset('assets/css/parts/header.css') }}?t={{ time() }}">
        <link rel="stylesheet" href="{{ asset('assets/css/parts/footer.css') }}?t={{ time() }}">
        <link rel="stylesheet" href="{{ asset('assets/css/parts/newsletter.css') }}?t={{ time() }}">
        <link rel="stylesheet" href="{{ asset('assets/css/parts/notification-toggle.css') }}?t={{ time() }}">

        <!-- Hide Google Translate elements -->
        <style>
            /* Hide all Google Translate elements */
            .goog-te-banner-frame,
            .skiptranslate,
            .goog-te-gadget-simple,
            #goog-gt-tt,
            .goog-tooltip,
            .goog-te-balloon-frame,
            div#goog-gt-,
            #google_translate_element,
            .goog-te-spinner-pos,
            .goog-te-spinner,
            .goog-te-sectional-gadget,
            .goog-te-menu-value,
            .goog-te-sectional-gadget-container,
            .goog-te-spinner-animation {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
                position: absolute !important;
                top: -9999px !important;
                left: -9999px !important;
                z-index: -9999 !important;
            }

            /* Remove translation bar space */
            body {
                top: 0 !important;
                position: static !important;
            }

            /* Hide Google's translation control elements */
            .gtx-trans-icon,
            .trans-target,
            .trans-verified-button,
            .trans-edit-button,
            .gt-ex-info,
            .trans-undo-button {
                display: none !important;
            }

            /* Hide overlay elements */
            .VIpgJd-ZVi9od-ORHb-OEVmcd,
            .VIpgJd-ZVi9od-ORHb,
            .VIpgJd-ZVi9od-SmfZ-OEVmcd,
            .VIpgJd-ZVi9od-xl07Ob-lTBxed,
            .VIpgJd-ZVi9od-vH1Gmf-ibnC6b,
            .VIpgJd-ZVi9od-l4eHX-hSRGPd,
            .goog-te-spinner-animation-container,
            .goog-te-sectional-gadget-container {
                display: none !important;
            }

            /* Fix for preventing selection/highlights on translated text */
            .notranslate-wrapper * {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }

            /* Restore user selection for form inputs */
            .notranslate-wrapper input,
            .notranslate-wrapper textarea,
            .notranslate-wrapper select {
                -webkit-user-select: auto !important;
                -moz-user-select: auto !important;
                -ms-user-select: auto !important;
                user-select: auto !important;
            }

            /* Add this class to any element that should not be translated */
            .notranslate {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }
        </style>

        @yield('styles')
    </head>
    <body>
        <!-- Top Bar -->
        @include('parts.top-bar')

        <!-- Header -->
        @include('parts.header')

        <!-- Main Content -->
        <div class="notranslate-wrapper">
            @yield('content')
        </div>

        <!-- Newsletter -->
        @include('parts.newsletter')

        <!-- Footer -->
        @include('parts.footer')

        <!-- JavaScript -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script src="{{ asset('assets/js/script.js') }}?t={{ time() }}"></script>

        <!-- GTranslate: https://gtranslate.io/ -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // تبسيط دالة الحصول على معلمات الرابط
                function getUrlParam(name) {
                    var results = new RegExp('[\?&]' + name + '=([^&#]*)').exec(window.location.href);
                    return results && results[1] ? decodeURIComponent(results[1]) : null;
                }

                // تبسيط دالة إنشاء الكوكيز
                function createCookie(name, value) {
                    var domain = window.location.hostname;
                    var rootDomain = domain.split('.').slice(-2).join('.');
                    document.cookie = name + "=" + value + "; path=/; domain=" + domain;
                    document.cookie = name + "=" + value + "; path=/";
                }

                // دالة الحصول على قيمة كوكي
                function getCookie(name) {
                    var match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
                    return match ? match[2] : null;
                }

                // إزالة كوكيز جوجل - تم تبسيطها
                function removeGoogleCookies() {
                    var domain = window.location.hostname;
                    var rootDomain = domain.split('.').slice(-2).join('.');
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=" + domain;
                    document.cookie = "googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=." + rootDomain;
                    localStorage.removeItem('googtrans');
                }

                // تنفيذ الترجمة - تم تحسينها للأداء
                function doGTranslate(lang_pair) {
                    var lang = lang_pair.split('|')[1];

                    if (lang != 'ar') {
                        // تغيير اتجاه الصفحة
                        document.documentElement.setAttribute('dir', 'ltr');
                        document.documentElement.setAttribute('lang', lang);

                        // تغيير CSS البوتستراب
                        var bootstrapCSS = document.querySelector('link[href*="bootstrap.rtl.min.css"]');
                        if (bootstrapCSS) {
                            bootstrapCSS.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
                        }

                        // إعداد كوكيز الترجمة بشكل صحيح ومباشر
                        var domain = window.location.hostname;
                        document.cookie = "googtrans=/ar/" + lang + "; path=/;";
                        document.cookie = "googtrans=/ar/" + lang + "; path=/; domain=" + domain;
                        localStorage.setItem('selectedLanguage', lang);

                        // تحديث الرابط وإعادة تحميل الصفحة فقط إذا لزم الأمر
                        var currentParam = getUrlParam('gtranslate');
                        if (currentParam !== lang) {
                            var url = new URL(window.location.href);
                            url.searchParams.set('gtranslate', lang);
                            window.location.href = url.toString();
                        } else {
                            // تطبيق الترجمة مباشرة بدون إعادة تحميل
                            triggerTranslation(lang);
                        }
                    } else {
                        // للغة العربية، إزالة الكوكيز والعودة إلى الوضع الأصلي
                        removeGoogleCookies();
                        document.documentElement.setAttribute('dir', 'rtl');
                        document.documentElement.setAttribute('lang', 'ar');

                        // تغيير CSS البوتستراب للـ RTL
                        var bootstrapCSS = document.querySelector('link[href*="bootstrap.min.css"]');
                        if (bootstrapCSS) {
                            bootstrapCSS.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css';
                        }

                        localStorage.setItem('selectedLanguage', 'ar');

                        // إعادة تحميل إلى الرابط الأساسي فقط إذا كان هناك معلمة gtranslate
                        if (getUrlParam('gtranslate')) {
                            var url = new URL(window.location.href);
                            url.searchParams.delete('gtranslate');
                            window.location.href = url.toString();
                        }
                    }
                }

                // تحسين أداء تطبيق الترجمة
                function triggerTranslation(lang) {
                    if (!lang || lang === 'ar') return;

                    var select = document.querySelector('.goog-te-combo');
                    if (select) {
                        select.value = lang;
                        select.dispatchEvent(new Event('change'));

                        // إخفاء عناصر جوجل بشكل أسرع
                        document.querySelectorAll('.skiptranslate, #goog-gt-tt').forEach(function(el) {
                            if (el) el.style.display = 'none';
                        });
                        document.body.style.top = '0px';

                        // معالجة التظليل للنص المترجم
                        fixTranslatedElements();
                    }
                }

                // تحسين معالجة عناصر النص المترجم
                function fixTranslatedElements() {
                    // إضافة notranslate لجميع عناصر الإدخال
                    document.querySelectorAll('input, select, textarea, button').forEach(function(el) {
                        el.classList.add('notranslate');
                    });

                    // تصحيح عناصر التظليل المضافة من جوجل
                    document.querySelectorAll('.VIpgJd-yAWNEb-VIpgJd-fmcmS-sn54Q').forEach(function(el) {
                        el.classList.add('notranslate');
                        el.style.userSelect = 'text';
                        el.style.backgroundColor = 'transparent';
                    });
                }

                // إضافة مستمعات الأحداث لروابط اللغة
                document.querySelectorAll('.language-selector .dropdown-item').forEach(function(link) {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        var lang = this.getAttribute('data-lang');
                        doGTranslate('ar|' + lang);
                    });
                });

                // التحقق من اللغة المخزنة أو معلمة الرابط عند تحميل الصفحة
                var lang = getUrlParam('gtranslate') || localStorage.getItem('selectedLanguage') || getCookie('googtrans');

                // استخراج اللغة من كوكي googtrans إذا وجد
                if (lang && lang.indexOf('/') > -1) {
                    lang = lang.split('/')[2];
                }

                // استخدام العربية كلغة افتراضية إذا لم توجد لغة محددة
                lang = lang || 'ar';

                // تحديث نص القائمة المنسدلة للغة الحالية
                var langElement = document.querySelector('[data-lang="' + lang + '"]');
                if (langElement && document.getElementById('languageDropdown')) {
                    document.getElementById('languageDropdown').innerHTML =
                        '<i class="fas fa-language"></i> ' + langElement.textContent;
                }

                // ضبط اتجاه الصفحة واللغة والـ CSS بناءً على اللغة المحددة
                if (lang && lang !== 'ar') {
                    document.documentElement.setAttribute('dir', 'ltr');
                    document.documentElement.setAttribute('lang', lang);

                    var bootstrapCSS = document.querySelector('link[href*="bootstrap.rtl.min.css"]');
                    if (bootstrapCSS) {
                        bootstrapCSS.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
                    }
                } else {
                    document.documentElement.setAttribute('dir', 'rtl');
                    document.documentElement.setAttribute('lang', 'ar');

                    var bootstrapCSS = document.querySelector('link[href*="bootstrap.min.css"]:not([href*="rtl"])');
                    if (bootstrapCSS) {
                        bootstrapCSS.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css';
                    }

                    var arLangElement = document.querySelector('[data-lang="ar"]');
                    if (arLangElement && document.getElementById('languageDropdown')) {
                        document.getElementById('languageDropdown').innerHTML =
                            '<i class="fas fa-language"></i> ' + arLangElement.textContent;
                    }

                    // التأكد من عدم وجود كوكيز ترجمة للعربية
                    removeGoogleCookies();
                }

                // إخفاء واجهة Google Translate عبر جافاسكريبت بشكل فوري
                document.body.style.top = '0px';
                document.body.style.position = 'static';
            });
        </script>

        @yield('scripts')
        <!-- Firebase Notifications for Visitors -->
        <x-visitor-messaging />

        <!-- Add GTranslate Widget -->
        <div id="google_translate_element" style="display:none"></div>
        <script type="text/javascript">
        function googleTranslateElementInit() {
            new google.translate.TranslateElement({
                pageLanguage: 'ar',
                includedLanguages: 'ar,en,fr,es,de,ru,zh-CN,it,pt,ja,ko,nl,tr,pl,hi,sv,th,cs,fi,el,he,id,da,ro,hu,vi,uk,ms,fa',
                layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                autoDisplay: false
            }, 'google_translate_element');
        }
        </script>
        <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

        <script>
            // تطبيق الترجمة بناءً على الرابط
            function initTranslation(lang) {
                // إذا لم تكن هناك لغة محددة أو كانت العربية، فلا تقم بالترجمة
                if (!lang || lang === 'ar') return;

                var select = document.querySelector('.goog-te-combo');
                if (select) {
                    select.value = lang;
                    select.dispatchEvent(new Event('change'));

                    // إخفاء شريط Google Translate بعد الترجمة
                    document.querySelectorAll('.skiptranslate').forEach(function(el) {
                        el.style.display = 'none';
                    });
                    document.body.style.top = '0px';

                    // محاولة إزالة الإطارات
                    document.querySelectorAll('iframe').forEach(function(iframe) {
                        if (iframe.src.indexOf('translate.google') >= 0) {
                            iframe.style.display = 'none';
                        }
                    });

                    // إضافة notranslate للعناصر المشكلة
                    document.querySelectorAll('.VIpgJd-yAWNEb-VIpgJd-fmcmS-sn54Q').forEach(function(el) {
                        el.classList.add('notranslate');
                        el.style.backgroundColor = 'transparent';
                    });
                }
            }

            // تشغيل بعد تحميل Google Translate
            window.addEventListener('load', function() {
                // الحصول على اللغة المحددة
                var selectedLang = new URLSearchParams(window.location.search).get('gtranslate') ||
                                   localStorage.getItem('selectedLanguage') ||
                                   document.cookie.match(/googtrans=\/ar\/([^;]+)/)?.[1];

                // تطبيق الترجمة فقط إذا كانت اللغة غير العربية
                if (selectedLang && selectedLang !== 'ar') {
                    // تطبيق الترجمة بشكل أسرع
                    setTimeout(function() {
                        initTranslation(selectedLang);
                    }, 300);
                }

                // محاولات إضافية لإخفاء عناصر Google
                setTimeout(function() {
                    // إخفاء أي إطارات Google
                    document.querySelectorAll('iframe').forEach(function(iframe) {
                        if (iframe.src.indexOf('translate.google') >= 0) {
                            iframe.style.display = 'none';
                        }
                    });

                    // إزالة هامش الجسم المضاف من Google
                    document.body.style.top = '0px';
                    document.body.style.position = 'static';
                }, 500);

                // مراقبة التغييرات في DOM لمعالجة العناصر المضافة بواسطة Google
                var observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                            mutation.addedNodes.forEach(function(node) {
                                if (node.classList && node.classList.contains('VIpgJd-yAWNEb-VIpgJd-fmcmS-sn54Q')) {
                                    node.classList.add('notranslate');
                                    node.style.userSelect = 'text';
                                    node.style.backgroundColor = 'transparent';
                                }
                            });
                        }
                    });
                });

                observer.observe(document.body, { childList: true, subtree: true });
            });
        </script>

        <style>
            /* Hide Google translate banner and elements */
            .goog-te-banner-frame,
            .goog-te-balloon-frame,
            #goog-gt-tt,
            .goog-tooltip,
            iframe[name=googleTranslateElementInit],
            .VIpgJd-ZVi9od-aZ2wEe {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }

            body {
                top: 0 !important;
                position: static !important;
            }

            .goog-te-gadget {
                height: 0 !important;
                overflow: hidden !important;
            }

            /* Hide all Google specific elements */
            body > .skiptranslate,
            body > iframe[src*="translate.google"],
            #goog-gt-,
            .goog-te-spinner-pos,
            .goog-te-spinner,
            div.goog-te-sectional-gadget,
            div.goog-te-gadget-simple {
                display: none !important;
                visibility: hidden !important;
            }

            /* Fix selection/highlight styles from Google Translate */
            .VIpgJd-yAWNEb-VIpgJd-fmcmS-sn54Q {
                background-color: transparent !important;
                box-shadow: none !important;
                display: inline !important;
                border: none !important;
                color: inherit !important;
                font-style: inherit !important;
                font-variant: inherit !important;
                font-weight: inherit !important;
                text-decoration: inherit !important;
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }

            /* Reset any spans added by Google */
            span[style*="vertical-align: inherit"] {
                vertical-align: inherit !important;
                background-color: transparent !important;
                display: inline !important;
            }

            /* Fix for background colors on translated text */
            font.VIpgJd-yAWNEb-VIpgJd-fmcmS-sn54Q {
                background-color: transparent !important;
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }
        </style>
    </body>
</html>
