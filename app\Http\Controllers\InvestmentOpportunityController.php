<?php

namespace App\Http\Controllers;

use App\Models\Investment;
use App\Models\InvestmentCategory;
use Illuminate\Http\Request;

class InvestmentOpportunityController extends Controller
{
    public function index(Request $request)
    {
        $categories = InvestmentCategory::whereHas('investments', function ($query) {
            $query->where('is_published', true);
        })->get();

        $query = Investment::where('is_published', true);

        if ($request->has('category') && $request->category !== 'all') {
            $query->where('investment_category_id', $request->category);
        }

        if ($request->has('location') && $request->location !== 'all') {
            $query->where('location', $request->location);
        }

        $locations = Investment::where('is_published', true)
            ->whereNotNull('location')
            ->distinct()
            ->pluck('location');

        $investments = $query->orderBy('published_at', 'desc')
            ->paginate(9)
            ->withQueryString();

        return view('investments.index', compact('investments', 'categories', 'locations'));
    }

    public function show(Investment $investment)
    {
        if ($investment->tags) {
            if (is_string($investment->tags)) {
                $investment->tags = json_decode($investment->tags) ?: [];
            }
        } else {
            $investment->tags = [];
        }

        if ($investment->investment_highlights) {
            if (is_string($investment->investment_highlights)) {
                $investment->investment_highlights = json_decode($investment->investment_highlights) ?: [];
            }
        } else {
            $investment->investment_highlights = [];
        }

        $galleryImages = $investment->images()->orderBy('sort_order')->get();

        if ($galleryImages->isEmpty() && $investment->featured_image) {
            $virtualGallery = true;
        } else {
            $virtualGallery = false;
        }

        $similarInvestments = Investment::where('id', '!=', $investment->id)
            ->where('is_published', true)
            ->where(function ($query) use ($investment) {
                $query->where('investment_category_id', $investment->investment_category_id)
                    ->orWhere('location', $investment->location)
                    ->orWhere(function ($q) use ($investment) {
                        if (!empty($investment->tags)) {
                            foreach ($investment->tags as $tag) {
                                $q->orWhere('tags', 'LIKE', '%' . $tag . '%');
                            }
                        }
                    });
            })
            ->orderByRaw('
                CASE
                    WHEN investment_category_id = ? THEN 1
                    WHEN location = ? THEN 2
                    ELSE 3
                END', [$investment->investment_category_id, $investment->location])
            ->latest()
            ->take(6)
            ->get();

        return view('investments.show', compact('investment', 'galleryImages', 'virtualGallery', 'similarInvestments'));
    }
}
