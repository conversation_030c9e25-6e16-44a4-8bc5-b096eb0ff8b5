/* Footer Styles */
footer {
    background-color: #f8f9fa;
    padding: 60px 0 30px;
    color: #333;
    position: relative;
    overflow: hidden;
    box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.05);
}

.footer-heading {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 10px;
}

.footer-heading:after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 60px;
    height: 3px;
    background-color: #00b5ad;
    border-radius: 3px;
}

/* Adding text-decoration: none for all links in footer */
footer a {
    text-decoration: none;
    transition: all 0.25s ease-in-out;
}

.contact-methods-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.contact-method-group {
    flex: 1;
    min-width: 200px;
}

.contact-method {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 18px;
    background-color: #fff;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    border-left: 3px solid transparent;
}

.contact-method:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    border-left: 3px solid #00b5ad;
}

.contact-method i {
    font-size: 22px;
    color: #00b5ad;
    transition: transform 0.3s ease;
}

.contact-method:hover i {
    transform: scale(1.2);
}

.contact-label {
    font-weight: 500;
    transition: all 0.3s ease;
}

.contact-method:hover .contact-label {
    transform: translateX(-5px);
}

.contact-form-container {
    background-color: transparent;
    padding: 35px;
    border-radius: 15px;
    box-shadow: none;
    transition: transform 0.3s ease;
}

.contact-form-container:hover {
    transform: translateY(-5px);
    box-shadow: none;
}

.footer-form-heading {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
    color: #00b5ad;
}

.footer-form .form-row {
    margin-bottom: 20px;
}

.footer-form .form-control {
    padding: 15px 18px;
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: 10px;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02) inset;
}

.footer-form .form-control:focus {
    border-color: rgba(0, 181, 173, 0.3);
    box-shadow: 0 0 0 3px rgba(0, 181, 173, 0.1);
    outline: none;
}

.footer-form .phone-row {
    display: flex;
    gap: 15px;
}

.footer-form .phone-row > * {
    flex: 1;
}

.footer-form .input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.footer-form .footer-country-code {
    position: absolute;
    right: 10px;
    color: #fff;
    background-color: #00b5ad;
    padding: 3px 10px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    top: 0;
    transform: translateY(-50%);
    margin-top: 0;
    display: flex;
    align-items: center;
    height: 26px;
    line-height: 1;
    box-shadow: 0 2px 8px rgba(0, 181, 173, 0.3);
}

.footer-form input[name="phone"] {
    padding-right: 60px;
}

.footer-form .footer-btn-submit {
    background-color: #ffffff;
    color: #00b5ad;
    border: 2px solid #00b5ad;
    padding: 15px 25px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.footer-form .footer-btn-submit:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: width 0.3s ease;
}

.footer-form .footer-btn-submit:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 181, 173, 0.15);
}

.footer-form .footer-btn-submit:hover:before {
    width: 100%;
}

.vision-copyright {
    text-align: center;
    margin-top: 50px;
    padding-top: 25px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.vision2030 {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.vision-logo {
    height: 65px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.vision-logo:hover {
    transform: scale(1.05);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #00b5ad;
    color: white;
    font-size: 18px;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(0, 181, 173, 0.2);
}

.social-links a:hover {
    background-color: #009d96;
    transform: translateY(-3px) rotate(8deg);
    box-shadow: 0 8px 15px rgba(0, 181, 173, 0.3);
}

.footer-copy {
    color: #777;
    font-size: 15px;
    margin-top: 15px;
    letter-spacing: 0.5px;
}

.chat-btns {
    position: fixed;
    bottom: 25px;
    left: 25px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    z-index: 1000;
}

.chat-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 65px;
    height: 65px;
    border-radius: 50%;
    color: white;
    font-size: 26px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-decoration: none;
}

.chat-btn:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
}

.chat-btn.whatsapp {
    background-color: #25d366;
}

.chat-btn.messenger {
    background-color: #0084ff;
}

.toast-message {
    position: fixed;
    top: 25px;
    right: 25px;
    z-index: 1000;
    max-width: 380px;
    padding: 18px 25px;
    border-radius: 12px;
    background-color: #d4edda;
    color: #155724;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    opacity: 1;
    transition: all 0.5s ease;
    direction: rtl;
    transform: translateY(0);
    border-right: 4px solid #28a745;
}

.toast-message.hide {
    opacity: 0;
    transform: translateY(-20px);
}

.toast-message i {
    margin-left: 10px;
    font-size: 18px;
    color: #28a745;
}

.contact-method-group a {
    text-decoration: none;
    color: inherit;
    display: block;
}

@media (max-width: 768px) {
    footer {
        padding: 40px 0 20px;
    }

    .contact-methods-grid {
        flex-direction: column;
        gap: 10px;
    }

    .contact-form-container {
        margin-top: 30px;
        padding: 25px;
    }

    .footer-form .phone-row {
        flex-direction: column;
        gap: 15px;
    }

    .footer-heading {
        font-size: 22px;
    }

    .chat-btn {
        width: 55px;
        height: 55px;
        font-size: 22px;
    }
}
