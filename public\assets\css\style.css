/* Global Styles */
:root {
    --primary-color: #00b5ad;
    --primary-dark: #008c85;
    --primary-light: #33c3bd;
    --secondary-color: #2c3e50;
    --text-color: #333;
    --text-light: #666;
    --white: #fff;
    --light-gray: #f8f9fa;
    --border-radius: 12px;
    --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: "Cairo", sans-serif;
    direction: rtl;
    text-align: right;
    color: var(--text-color);
    margin: 0;
    padding: 0;
    background-color: var(--light-gray);
    line-height: 1.8;
    font-size: 18px;
}

/* LTR (English) Layout Settings */
html[dir="ltr"] body {
    direction: ltr;
    text-align: left;
}

/* Reverse RTL-specific elements in LTR mode */
html[dir="ltr"] .pillar-item {
    padding-right: 15px;
    padding-left: 30px;
    border-right: none;
    border-left: 4px solid var(--primary-color);
    text-align: left;
}

html[dir="ltr"] .pillar-item:hover {
    transform: translateX(5px);
}

html[dir="ltr"] .checklist-item:hover {
    transform: translateX(5px);
}

html[dir="ltr"] .contact-method:hover {
    transform: translateX(5px);
}

html[dir="ltr"] .contact-heading::after {
    right: auto;
    left: 0;
}

html[dir="ltr"] .why-choose-text h3 {
    text-align: left;
}

html[dir="ltr"] .why-choose-text p {
    text-align: left;
}

html[dir="ltr"] .services-desc {
    text-align: center;
}

/* Move question mark to end of title in English */
html[dir="ltr"] .section-title:contains("?") {
    position: relative;
}

html[dir="ltr"] .section-title:contains("?")::before {
    content: "";
}

html[dir="ltr"] .section-title:contains("?")::after {
    content: attr(data-content) " ?";
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Hero Section */
.hero {
    background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.4)),
        url("../img/start-your-project/sec_chemplnt82-1-1024x430.jpg");
    background-size: cover;
    background-position: center;
    height: 600px;
    position: relative;
    color: var(--white);
    display: flex;
    align-items: center;
    margin-bottom: 50px;
}

.hero-content {
    text-align: right;
    max-width: 700px;
}

/* توسيط النص في قسم Hero عند تغيير اللغة */
html[dir="ltr"] .hero-content {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}

html[dir="ltr"] .hero-btns {
    justify-content: center;
}

.hero-content h5 {
    font-size: 28px;
    margin-bottom: 20px;
    color: var(--primary-color);
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-content h1 {
    font-size: 56px;
    margin-bottom: 30px;
    line-height: 1.3;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.hero-btns {
    display: flex;
    gap: 20px;
}

.btn-green,
.btn-whatsapp {
    padding: 14px 30px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.btn-green {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-whatsapp {
    background-color: #25d366;
    color: var(--white);
}

.btn-green:hover,
.btn-whatsapp:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 80px 0;
    background-color: var(--white);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
}

.section-title {
    text-align: center;
    color: var(--secondary-color);
    margin-bottom: 50px;
    font-size: 42px;
    font-weight: 700;
    position: relative;
}

.section-title::after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.why-choose-content {
    display: flex;
    gap: 60px;
    align-items: flex-start;
}

.why-choose-text {
    flex: 1;
}

.why-choose-text h3 {
    color: var(--primary-color);
    font-size: 32px;
    margin-bottom: 25px;
    font-weight: 700;
}

.why-choose-img {
    flex: 1;
}

.why-choose-img img {
    width: 100%;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.why-choose-img img:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.pillar-item {
    margin-bottom: 30px;
    padding-right: 30px;
    position: relative;
    background-color: var(--light-gray);
    padding: 20px 30px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border-right: 4px solid var(--primary-color);
}

.pillar-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--box-shadow);
}

.pillar-item h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 24px;
    font-weight: 700;
}

.pillar-item p {
    color: var(--text-light);
    font-size: 18px;
    line-height: 1.8;
}

/* Services Section */
.services-section {
    padding: 80px 0;
    background-color: #fff;
    position: relative;
}

.services-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><rect width="2" height="2" fill="%23f1f1f1" /></svg>');
    background-size: 30px 30px;
    opacity: 0.5;
    z-index: 0;
}

.services-section .container {
    position: relative;
    z-index: 1;
}

.services-desc {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 60px;
}

.services-desc p {
    margin-bottom: 12px;
    color: var(--text-light);
    font-size: 16px;
    line-height: 1.7;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 50px;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.service-item {
    text-align: center;
    padding: 30px 20px;
    border: 2px solid var(--primary-light);
    border-radius: var(--border-radius);
    transition: var(--transition);
    background-color: var(--white);
    box-shadow: 0 5px 15px rgba(0, 181, 173, 0.1);
}

.service-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 181, 173, 0.2);
    border-color: var(--primary-color);
}

.service-icon {
    margin-bottom: 20px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-icon img {
    width: 70px;
    height: 70px;
    object-fit: contain;
    transition: var(--transition);
}

.service-item:hover .service-icon img {
    transform: scale(1.1) rotate(5deg);
}

.service-item h3 {
    color: var(--secondary-color);
    font-size: 22px;
    font-weight: 600;
    margin: 0;
}

.services-checklist {
    max-width: 800px;
    margin: 0 auto;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    background-color: var(--light-gray);
    padding: 15px 20px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.checklist-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--box-shadow);
}

.checklist-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: var(--primary-color);
}

.checklist-item label {
    color: var(--text-color);
    font-size: 18px;
    margin: 0;
    font-weight: 500;
}

/* Stats Section */
.stats-section {
    padding: 80px 0;
    background: #00b5ad;
    border-radius: 20px;
    margin: 40px 20px;
    overflow: hidden;
    color: var(--white);
    position: relative;
}

.stats-section .section-title {
    color: var(--white);
    margin-bottom: 20px;
}

.stats-section .section-desc {
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 50px;
    font-size: 18px;
}

.stats-container {
    margin-top: 40px;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 60px;
    font-weight: 800;
    color: var(--white);
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 22px;
    color: var(--white);
    font-weight: 500;
}

/* Services Offering Section */
.services-offering {
    padding: 80px 0;
    background-color: var(--white);
}

.services-offering .section-title {
    margin-bottom: 30px;
}

.services-offering-desc {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 50px;
}

.services-offering-desc p {
    color: var(--text-light);
    font-size: 18px;
    line-height: 1.7;
}

.services-icons {
    padding: 30px 0;
}

.service-icon-item {
    text-align: center;
    padding: 30px 15px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.service-icon-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow);
    background-color: var(--white);
}

.service-icon-img {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    border-radius: 50%;
    transition: var(--transition);
}

.service-icon-img img {
    width: 45px;
    height: 45px;
    object-fit: contain;
    filter: brightness(0) invert(1);
}

.service-icon-item:hover .service-icon-img {
    transform: rotate(15deg);
}

.service-icon-title {
    color: var(--text-color);
    font-size: 22px;
    font-weight: 600;
    margin: 0;
    line-height: 1.4;
}

/* Invest Section */
.invest-section {
    padding: 80px 0;
    background-color: #f0f5f5;
    position: relative;
    overflow: hidden;
}

.invest-section::before {
    content: "";
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background-color: rgba(0, 181, 173, 0.1);
    border-radius: 50%;
}

.invest-section::after {
    content: "";
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 150px;
    height: 150px;
    background-color: rgba(0, 181, 173, 0.1);
    border-radius: 50%;
}

.invest-section .section-title {
    margin-bottom: 20px;
}

.invest-desc {
    text-align: center;
    color: var(--text-light);
    margin-bottom: 40px;
    font-size: 18px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.invest-form {
    background-color: var(--white);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.invest-form .form-group {
    margin-bottom: 20px;
}

.invest-form .form-control {
    height: 50px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: 10px 15px;
    width: 100%;
    font-size: 16px;
    transition: var(--transition);
}

.invest-form .form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 181, 173, 0.1);
}

.submit-btn {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    height: 50px;
    padding: 0 40px;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 181, 173, 0.2);
}

.submit-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 181, 173, 0.3);
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background-color: var(--white);
}

.contact-info-wrapper {
    background-color: var(--light-gray);
    padding: 40px;
    border-radius: var(--border-radius);
    height: 100%;
}

.contact-heading {
    color: var(--primary-color);
    font-size: 32px;
    margin-bottom: 30px;
    font-weight: 700;
    position: relative;
}

.contact-heading::after {
    content: "";
    position: absolute;
    bottom: -10px;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
}

.contact-methods {
    margin-bottom: 40px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--white);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.contact-method:hover {
    transform: translateX(-5px);
    box-shadow: var(--box-shadow);
}

.contact-method i {
    font-size: 20px;
    color: var(--primary-color);
}

.contact-method span {
    font-size: 18px;
    color: var(--text-color);
}

.social-block {
    text-align: center;
}

.social-block h5 {
    color: var(--text-color);
    font-size: 22px;
    margin-bottom: 20px;
    font-weight: 600;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-link {
    width: 45px;
    height: 45px;
    background-color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 18px;
    transition: var(--transition);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.social-link:hover {
    transform: translateY(-5px);
    background-color: var(--primary-color);
    color: var(--white);
    box-shadow: 0 5px 15px rgba(0, 181, 173, 0.2);
}

.contact-form {
    background-color: var(--primary-color);
    padding: 40px;
    border-radius: var(--border-radius);
    height: 100%;
    box-shadow: var(--box-shadow);
}

.contact-form h3 {
    color: var(--white);
    font-size: 32px;
    margin-bottom: 30px;
    font-weight: 700;
    text-align: center;
    position: relative;
}

.contact-form h3::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--white);
    border-radius: 2px;
}

.contact-form .form-control {
    height: auto;
    padding: 15px;
    border: none;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    width: 100%;
}

.contact-form textarea.form-control {
    height: 120px;
    resize: none;
}

.form-submit {
    background-color: var(--secondary-color);
    color: var(--white);
    border: none;
    padding: 15px;
    width: 100%;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-submit:hover {
    background-color: var(--secondary-color);
    opacity: 0.9;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    body {
        font-size: 16px;
    }

    .hero-content h1 {
        font-size: 48px;
    }

    .section-title {
        font-size: 36px;
    }

    .why-choose-text h3 {
        font-size: 28px;
    }

    .pillar-item h4 {
        font-size: 22px;
    }

    .pillar-item p {
        font-size: 16px;
    }

    .service-item h3 {
        font-size: 20px;
    }

    .checklist-item label {
        font-size: 16px;
    }

    .stat-number {
        font-size: 42px;
    }

    .stat-label {
        font-size: 18px;
    }

    .service-icon-title {
        font-size: 18px;
    }

    .contact-heading,
    .contact-form h3 {
        font-size: 28px;
    }

    .contact-method span {
        font-size: 16px;
    }

    .social-block h5 {
        font-size: 18px;
    }

    .hero-content h5 {
        font-size: 28px;
        margin-bottom: 20px;
    }

    .hero-content h1 {
        font-size: 56px;
        margin-bottom: 30px;
    }

    .why-choose-content {
        flex-direction: column;
        gap: 40px;
    }

    .hero-btns {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .contact-info-wrapper {
        margin-bottom: 30px;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .service-icon-item {
        padding: 20px 15px;
    }

    .service-icon-img {
        width: 70px;
        height: 70px;
    }
}

@media (max-width: 768px) {
    body {
        font-size: 15px;
    }

    .hero-content h1 {
        font-size: 36px;
    }

    .hero-content h5 {
        font-size: 22px;
    }

    .section-title {
        font-size: 32px;
    }

    .why-choose-text h3 {
        font-size: 26px;
    }

    .pillar-item h4 {
        font-size: 20px;
    }

    .pillar-item p {
        font-size: 15px;
    }

    .service-item h3 {
        font-size: 18px;
    }

    .checklist-item label {
        font-size: 15px;
    }

    .stat-number {
        font-size: 36px;
    }

    .stat-label {
        font-size: 16px;
    }

    .service-icon-title {
        font-size: 16px;
    }

    .contact-heading,
    .contact-form h3 {
        font-size: 26px;
    }

    .contact-method span {
        font-size: 15px;
    }

    .social-block h5 {
        font-size: 14px;
    }

    .hero {
        height: 450px;
        margin-bottom: 30px;
    }

    .hero-content h1 {
        font-size: 32px;
        margin-bottom: 20px;
    }

    .hero-content h5 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .section-title {
        font-size: 28px;
        margin-bottom: 30px;
    }

    .why-choose-text h3 {
        font-size: 24px;
        margin-bottom: 20px;
    }

    .pillar-item {
        padding: 15px;
        margin-bottom: 20px;
    }

    .invest-form {
        padding: 20px;
    }

    .service-icon-img {
        width: 60px;
        height: 60px;
    }

    .service-icon-img img {
        width: 35px;
        height: 35px;
    }

    .stats-section {
        padding: 40px 0;
        margin: 20px 10px;
    }

    .stat-item {
        padding: 20px 15px;
        margin-bottom: 15px;
    }

    .services-section,
    .services-offering,
    .invest-section {
        padding: 40px 0;
    }
}

@media (max-width: 576px) {
    body {
        font-size: 14px;
    }

    .hero-content h1 {
        font-size: 30px;
    }

    .hero-content h5 {
        font-size: 18px;
    }

    .section-title {
        font-size: 26px;
    }

    .why-choose-text h3 {
        font-size: 22px;
    }

    .pillar-item h4 {
        font-size: 18px;
    }

    .pillar-item p {
        font-size: 14px;
    }

    .service-item h3 {
        font-size: 16px;
    }

    .checklist-item label {
        font-size: 14px;
    }

    .stat-number {
        font-size: 32px;
    }

    .stat-label {
        font-size: 14px;
    }

    .service-icon-title {
        font-size: 14px;
    }

    .contact-heading,
    .contact-form h3 {
        font-size: 22px;
    }

    .contact-method span {
        font-size: 14px;
    }

    .social-block h5 {
        font-size: 14px;
    }

    .hero {
        height: 400px;
    }

    .hero-content h5 {
        font-size: 16px;
    }

    .hero-content h1 {
        font-size: 26px;
    }

    .btn-green,
    .btn-whatsapp {
        padding: 10px 15px;
        font-size: 14px;
        width: 100%;
        text-align: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .services-desc p {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .checklist-item {
        padding: 12px 15px;
        margin-bottom: 10px;
    }

    .checklist-item label {
        font-size: 14px;
    }

    .stats-section .section-title,
    .services-offering .section-title,
    .contact-heading,
    .contact-form h3 {
        font-size: 22px;
    }

    .service-icon-img {
        width: 50px;
        height: 50px;
        margin-bottom: 12px;
    }

    .service-icon-title {
        font-size: 14px;
    }

    .contact-heading,
    .contact-method span,
    .services-offering-desc p,
    .invest-desc {
        font-size: 14px;
    }

    .services-offering .section-title {
        margin-bottom: 15px;
    }

    .services-offering-desc p {
        margin-bottom: 20px;
    }

    .service-icon-item {
        margin-bottom: 15px;
        padding: 15px 10px;
    }

    .invest-form {
        padding: 15px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .submit-btn {
        height: 45px;
        font-size: 14px;
    }
}

/* Additional LTR styles */
html[dir="ltr"] .btn-green i,
html[dir="ltr"] .btn-whatsapp i {
    margin-right: 5px;
    margin-left: 0;
}

html[dir="ltr"] .contact-method {
    flex-direction: row-reverse;
}

html[dir="ltr"] .contact-info-wrapper,
html[dir="ltr"] .contact-form {
    text-align: left;
}

html[dir="ltr"] .contact-form h3::after {
    left: 0;
    right: auto;
}

html[dir="ltr"] .service-item {
    text-align: center;
}

html[dir="ltr"] .service-icon-item {
    text-align: center;
}

html[dir="ltr"] .invest-desc,
html[dir="ltr"] .invest-form {
    text-align: left;
}

html[dir="ltr"] .stats-section .section-desc,
html[dir="ltr"] .services-offering-desc {
    text-align: center;
}

html[dir="ltr"] .stats-section .section-title::after,
html[dir="ltr"] .services-offering .section-title::after {
    left: 50%;
    right: auto;
}

html[dir="ltr"] .stat-item,
html[dir="ltr"] .service-icon-img {
    text-align: center;
}

/* Fix hero button alignment in LTR mode */
html[dir="ltr"] .hero-btns {
    flex-direction: row;
    justify-content: center;
}

/* Fix toast message in LTR mode */
html[dir="ltr"] .toast-message {
    right: auto;
    left: 20px;
    direction: ltr;
}

/* Move question mark to end of title in English */
html[dir="ltr"] .section-title[data-has-question-mark="true"] {
    position: relative;
}

html[dir="ltr"] .section-title[data-has-question-mark="true"]::before {
    content: "";
}

html[dir="ltr"] .section-title[data-has-question-mark="true"]::after {
    content: "?";
    display: inline-block;
    margin-left: 2px;
}
