<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ContactPageController;
use App\Http\Controllers\InvestmentController;
use App\Http\Controllers\StartProjectController;
use App\Http\Controllers\NewsletterController;
use App\Http\Controllers\InvestmentOpportunityController;
use App\Http\Controllers\FCMController;
use App\Http\Controllers\VisitorFCMController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\PortfolioController;

Route::get('/', function () {
    return view('index');
});

// Blog Routes (Frontend - Public)
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{blog:slug}', [BlogController::class, 'show'])->name('blog.show');

// Portfolio Routes (Frontend - Public)
Route::get('/portfolio', [PortfolioController::class, 'index'])->name('portfolio.index');
Route::get('/portfolio/{portfolio:slug}', [PortfolioController::class, 'show'])->name('portfolio.show');

// Static Pages Routes
Route::get('/about-us', function () {
    return view('about-us');
})->name('about-us');

Route::get('/testimonials', function () {
    return view('testimonials');
})->name('testimonials');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::get('/services', function () {
    return view('services');
})->name('services');

Route::get('/start-your-project', function () {
    return view('start-your-project');
})->name('start-your-project');

Route::get('/thank-you', function () {
    return view('thank-you');
})->name('thank-you');

// منع الوصول إلى صفحة التسجيل وإعادة التوجيه إلى صفحة تسجيل الدخول
Route::get('/register', function () {
    return redirect()->route('login');
})->middleware(['basic.auth']);

Route::post('/register', function () {
    abort(403, 'التسجيل غير متاح حالياً');
})->middleware(['basic.auth']);


// Contact Routes
Route::post('/contact/submit', [ContactController::class, 'submit'])
    ->name('contact.submit')
    ->middleware(['web']);

// Contact Page Form Route
Route::post('/contact-page/submit', [ContactPageController::class, 'submit'])
    ->name('contact.page.submit')
    ->middleware(['web']);

// Investment Routes
Route::post('/investment/submit', [InvestmentController::class, 'submit'])
    ->name('investment.submit')
    ->middleware(['web']);

// Start Project Route
Route::post('/start-project/submit', [StartProjectController::class, 'submit'])
    ->name('start.project.submit')
    ->middleware(['web']);

// Newsletter Subscription Route
Route::post('/newsletter/subscribe', [NewsletterController::class, 'subscribe'])
    ->name('newsletter.subscribe')
    ->middleware(['web']);

// Investment Opportunities Routes (Frontend - Public)
Route::get('/investment-opportunities', [InvestmentOpportunityController::class, 'index'])->name('investments.index');
Route::get('/investment-opportunities/{investment:slug}', [InvestmentOpportunityController::class, 'show'])->name('investments.show');

// Blog Notifications Routes

// FCM Token Update Route
Route::post('/fcm/token/update', [App\Http\Controllers\FCMController::class, 'updateToken'])
    ->name('fcm.token.update')
    ->middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified']);

// إضافة مسارات إشعارات الزوار
Route::post('/visitor/fcm/token/update', [App\Http\Controllers\VisitorFCMController::class, 'updateToken'])
    ->name('visitor.fcm.token.update')
    ->middleware(['web']);

Route::post('/visitor/fcm/unsubscribe', [App\Http\Controllers\VisitorFCMController::class, 'unsubscribe'])
    ->name('visitor.fcm.unsubscribe')
    ->middleware(['web']);
