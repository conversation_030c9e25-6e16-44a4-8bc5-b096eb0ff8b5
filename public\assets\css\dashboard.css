/* Dashboard Modern Styles - خبراء */
:root {
  --primary-color: #4FD1C5;
  --primary-hover: #38B2AC;
  --secondary-color: #4A5568;
  --success-color: #48BB78;
  --info-color: #4299E1;
  --warning-color: #ECC94B;
  --danger-color: #F56565;
  --light-color: #F7FAFC;
  --dark-color: #1A202C;
  --transition-speed: 0.3s;
  --border-radius: 16px;
  --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.09);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.12);
}

body {
  background-color: #f5f7fa;
}

/* General Dashboard Styles */
.dashboard-container {
  background-color: #f9fafb;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-speed) ease;
  margin-bottom: 1.5rem;
}

.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-speed) ease;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header h4 {
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 0;
  position: relative;
}

.card-header h4:after {
  content: '';
  position: absolute;
  bottom: -8px;
  right: 0;
  height: 3px;
  width: 40px;
  background: linear-gradient(to right, var(--primary-color), var(--info-color));
}

.card-body {
  padding: 1.5rem;
}

/* Stat Cards */
.stat-card {
  position: relative;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  overflow: hidden;
  display: flex;
  align-items: center;
  min-height: 160px;
  box-shadow: var(--shadow-lg);
  background-size: 300% 300%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.stat-card.primary {
  background: linear-gradient(135deg, #4FACFE 0%, #00F2FE 100%);
}

.stat-card.success {
  background: linear-gradient(135deg, #43E97B 0%, #38F9D7 100%);
}

.stat-card.info {
  background: linear-gradient(135deg, #0093E9 0%, #80D0C7 100%);
}

.stat-card .stat-icon {
  position: absolute;
  left: -15px;
  bottom: -15px;
  font-size: 6rem;
  opacity: 0.15;
  transform: rotate(-10deg);
  transition: all 0.5s ease;
}

.stat-card:hover .stat-icon {
  opacity: 0.25;
  transform: rotate(0deg) scale(1.1);
}

.stat-card .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.stat-card .card-text {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Quick Links */
.quick-links-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-links-header h5 {
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 0;
  position: relative;
}

.quick-links-header h5:after {
  content: '';
  position: absolute;
  bottom: -8px;
  right: 0;
  height: 3px;
  width: 30px;
  background: linear-gradient(to right, var(--primary-color), var(--info-color));
}

.list-group {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.list-group-item {
  border: none;
  padding: 1.25rem;
  margin-bottom: 0.5rem;
  border-radius: 12px !important;
  transition: all var(--transition-speed) ease;
  background-color: #fff;
  box-shadow: var(--shadow-sm);
  border-right: 3px solid transparent;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.list-group-item:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 0;
  background-color: rgba(79, 209, 197, 0.05);
  z-index: -1;
  transition: all 0.3s ease;
}

.list-group-item:hover {
  background-color: #fff;
  border-right: 3px solid var(--primary-color);
  transform: translateX(-8px);
  box-shadow: var(--shadow-md);
}

.list-group-item:hover:before {
  width: 100%;
}

.list-group-item i {
  width: 36px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(79, 209, 197, 0.1);
  color: var(--primary-color);
  border-radius: 10px;
  margin-left: 0.75rem;
  transition: all var(--transition-speed) ease;
}

.list-group-item:hover i {
  background-color: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .stat-card {
    margin-bottom: 1rem;
  }

  .stat-card .card-text {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .card-header h4:after {
    width: 30px;
  }

  .quick-links-header h5:after {
    width: 20px;
  }

  .stat-card {
    min-height: 130px;
  }

  .stat-card .card-text {
    font-size: 2rem;
  }
}

/* Animation Effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.5s ease forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }

/* Modern Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Custom Dashboard Components */
.welcome-message {
  background: linear-gradient(135deg, #f6f8fb 0%, #f1f5f9 100%);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.welcome-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--info-color));
}

.welcome-message h3 {
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.welcome-message p {
  color: var(--secondary-color);
  margin-bottom: 0;
}

/* Progress Indicators */
.progress {
  height: 10px;
  border-radius: 5px;
  background-color: #e9ecef;
  margin-bottom: 1rem;
  overflow: visible;
}

.progress-bar {
  position: relative;
  border-radius: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--info-color));
}

.progress-bar::after {
  content: attr(data-value);
  position: absolute;
  top: -20px;
  right: 0;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--dark-color);
}
