<?php

namespace App\Http\Controllers;

use App\Models\VisitorToken;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class VisitorFCMController extends Controller
{
    /**
     * تحديث أو إنشاء توكن FCM للزائر
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateToken(Request $request)
    {
        try {
            $request->validate([
                'token' => 'required|string',
                'device_id' => 'required|string',
            ]);

            $token = $request->token;
            $deviceId = $request->device_id;

            // تحقق من صحة التوكن
            if (empty($token) || $token === 'null' || $token === 'undefined' || strlen($token) < 10) {
                $token = null;
                Log::info('Setting visitor token to NULL due to invalid value', [
                    'original_token' => $request->token,
                    'device_id' => $deviceId
                ]);
            }

            // تحديث أو إنشاء سجل جديد
            $visitorToken = VisitorToken::updateOrCreate(
                ['device_id' => $deviceId],
                [
                    'fcm_token' => $token,
                    'browser' => $request->browser ?? null,
                    'os' => $request->os ?? null,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'last_active_at' => now(),
                    'is_active' => $token ? true : false,
                    'is_subscribed' => $token ? true : false,
                ]
            );

            Log::info('Visitor FCM token updated', [
                'device_id' => $deviceId,
                'token_status' => $token ? 'valid_token' : 'cleared_token',
                'token_preview' => $token ? substr($token, 0, 10) . '...' : 'NULL',
                'subscription_status' => $token ? 'subscribed' : 'unsubscribed'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Visitor FCM token updated successfully',
                'token_status' => $token ? 'saved' : 'cleared',
                'is_subscribed' => $token ? true : false
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating visitor FCM token', [
                'error' => $e->getMessage(),
                'device_id' => $request->device_id ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update FCM token: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إلغاء اشتراك زائر في الإشعارات
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unsubscribe(Request $request)
    {
        try {
            $request->validate([
                'device_id' => 'required|string',
            ]);

            $deviceId = $request->device_id;

            $visitorToken = VisitorToken::where('device_id', $deviceId)->first();

            if ($visitorToken) {
                $visitorToken->is_subscribed = false;
                $visitorToken->save();

                Log::info('Visitor unsubscribed from notifications', [
                    'device_id' => $deviceId
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Unsubscribed successfully',
                    'is_subscribed' => false
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Device not found'
            ], 404);

        } catch (\Exception $e) {
            Log::error('Error unsubscribing visitor', [
                'error' => $e->getMessage(),
                'device_id' => $request->device_id ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to unsubscribe: ' . $e->getMessage()
            ], 500);
        }
    }
}
