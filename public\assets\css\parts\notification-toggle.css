/* Notification Toggle Button */
.notification-toggle-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    color: #fff;
    margin: 0 8px;
    padding: 4px 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-toggle-btn i {
    font-size: 18px;
    transition: all 0.3s ease;
}

.notification-toggle-btn::after {
    content: '';
    position: absolute;
    top: 4px;
    right: 3px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #dc3545;
    opacity: 0;
    transition: all 0.3s ease;
}

/* Active state - when subscribed to notifications */
.notification-toggle-btn.active i {
    color: #28a745;
}

.notification-toggle-btn.active::after {
    opacity: 1;
    background-color: #28a745;
}

/* Hover effects */
.notification-toggle-btn:hover {
    transform: scale(1.1);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .notification-toggle-btn {
        margin: 0 5px;
    }
}

/* Footer Notification Button */
.chat-btn.notification {
    background-color: #6c757d;
    border: none;
    cursor: pointer;
}

.chat-btn.notification:hover {
    background-color: #5a6268;
}

.chat-btn.notification.active {
    background-color: #28a745;
}

.chat-btn.notification.active i {
    color: #fff;
}

.chat-btn.notification::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #dc3545;
    opacity: 0;
    transition: all 0.3s ease;
}

.chat-btn.notification.active::after {
    opacity: 1;
    background-color: #fff;
}

/* Toast Notifications */
.notifications-toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notifications-toast {
    min-width: 280px;
    max-width: 350px;
    background-color: #fff;
    color: #333;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    overflow: hidden;
    transition: all 0.3s ease;
    animation: toast-slide-in 0.5s ease forwards;
}

.notifications-toast.fade-out {
    animation: toast-slide-out 0.3s ease forwards;
}

.notifications-toast-content {
    display: flex;
    align-items: center;
    padding: 12px 15px;
}

.notifications-toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
}

.notifications-toast.success .notifications-toast-icon {
    color: #28a745;
}

.notifications-toast.error .notifications-toast-icon {
    color: #dc3545;
}

.notifications-toast.warning .notifications-toast-icon {
    color: #ffc107;
}

.notifications-toast.info .notifications-toast-icon {
    color: #17a2b8;
}

.notifications-toast-message {
    flex-grow: 1;
    font-size: 14px;
}

.notifications-toast-close {
    cursor: pointer;
    margin-left: 10px;
    color: #999;
    transition: color 0.3s ease;
}

.notifications-toast-close:hover {
    color: #333;
}

/* Toast animations */
@keyframes toast-slide-in {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toast-slide-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(-100%);
        opacity: 0;
    }
}

/* RTL adaptations for toast */
html[dir="rtl"] .notifications-toast-container {
    left: auto;
    right: 20px;
}

html[dir="rtl"] .notifications-toast-icon {
    margin-right: 0;
    margin-left: 12px;
}

html[dir="rtl"] .notifications-toast-close {
    margin-left: 0;
    margin-right: 10px;
}

@keyframes rtl-toast-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes rtl-toast-slide-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

html[dir="rtl"] .notifications-toast {
    animation: rtl-toast-slide-in 0.5s ease forwards;
}

html[dir="rtl"] .notifications-toast.fade-out {
    animation: rtl-toast-slide-out 0.3s ease forwards;
}
