/* Testimonials Page Styles */

/* Hero Section */
.testimonials-hero {
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6)),
        url("https://images.unsplash.com/photo-1552664730-d307ca884978?auto=format&fit=crop&q=80&w=2070&ixlib=rb-4.0.3");
    background-size: cover;
    background-position: center;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background-color: var(--white);
}

/* Featured Testimonial */
.featured-testimonial {
    display: flex;
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    margin-bottom: 60px;
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.featured-testimonial-img {
    flex: 0 0 40%;
    background-size: cover;
    background-position: center;
    position: relative;
}

.featured-testimonial-content {
    flex: 0 0 60%;
    padding: 40px;
    position: relative;
}

.featured-testimonial-text {
    font-size: 20px;
    line-height: 1.7;
    color: var(--text-color);
    position: relative;
    padding-right: 25px;
}

.featured-testimonial-text::before {
    content: '\201D';
    font-size: 80px;
    position: absolute;
    top: -30px;
    right: -15px;
    color: var(--primary-color);
    opacity: 0.2;
    font-family: serif;
}

.featured-testimonial-author {
    display: flex;
    align-items: center;
    margin-top: 30px;
}

.featured-testimonial-avatar {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--primary-color);
}

.featured-testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-testimonial-info {
    margin-right: 20px;
}

.featured-testimonial-info h4 {
    margin: 0 0 5px;
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.featured-testimonial-info p {
    margin: 0;
    font-size: 14px;
    color: var(--text-light);
}

/* Testimonial Cards */
.testimonials-carousel {
    position: relative;
    padding: 30px 0;
    margin-bottom: 80px;
}

.testimonial-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    position: relative;
    height: 100%;
    transition: all 0.4s ease;
    opacity: 1;
    transform: translateY(0);
    border-top: 4px solid var(--primary-color);
}

.testimonial-quote {
    position: absolute;
    top: 20px;
    right: 20px;
    color: var(--primary-color);
    opacity: 0.2;
    font-size: 24px;
}

.testimonial-text {
    font-size: 16px;
    line-height: 1.7;
    color: var(--text-color);
    margin-bottom: 20px;
    min-height: 120px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 20px;
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-left: 15px;
    border: 2px solid var(--primary-color);
}

.testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-info {
    flex-grow: 1;
}

.testimonial-info h4 {
    margin: 0 0 5px;
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
}

.testimonial-info p {
    margin: 0 0 5px;
    font-size: 14px;
    color: var(--text-light);
}

.testimonial-rating {
    color: #ffc107;
    font-size: 14px;
}

/* Swiper Navigation & Pagination Customization */
.testimonials-swiper {
    padding-bottom: 50px;
}

.swiper-pagination {
    bottom: 0 !important;
}

.swiper-pagination-bullet {
    width: 12px !important;
    height: 12px !important;
    background-color: var(--primary-color) !important;
    opacity: 0.5 !important;
}

.swiper-pagination-bullet-active {
    opacity: 1 !important;
    background-color: var(--primary-color) !important;
}

.swiper-button-next,
.swiper-button-prev {
    color: var(--primary-color) !important;
    width: 40px !important;
    height: 40px !important;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 18px !important;
    font-weight: bold !important;
}

/* Video Testimonials */
.video-testimonials {
    padding: 20px 0;
}

.video-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.video-testimonial {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.video-testimonial:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    width: 100%;
    height: 250px;
    object-fit: cover;
    display: block;
    transition: all 0.5s ease;
}

.video-testimonial:hover .video-thumbnail {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.video-testimonial:hover .video-overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.play-button {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    transition: all 0.3s ease;
}

.video-testimonial:hover .play-button {
    transform: scale(1.1);
    background-color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 991px) {
    .featured-testimonial {
        flex-direction: column;
    }

    .featured-testimonial-img {
        height: 250px;
        flex: none;
        width: 100%;
    }

    .featured-testimonial-content {
        flex: none;
        width: 100%;
    }

    .testimonial-text {
        min-height: auto;
    }

    .video-testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials-swiper .swiper-slide {
        width: 100% !important;
    }
}

@media (max-width: 768px) {
    .testimonials-section {
        padding: 50px 0;
    }

    .featured-testimonial-text {
        font-size: 18px;
    }

    .featured-testimonial-content {
        padding: 30px;
    }

    .testimonial-card {
        padding: 25px;
    }

    .swiper-button-next,
    .swiper-button-prev {
        display: none !important;
    }

    .testimonials-swiper .swiper-wrapper {
        padding-bottom: 20px;
    }

    /* Fix Swiper display on mobile */
    .swiper-container {
        overflow: hidden !important;
    }

    .swiper-slide {
        height: auto !important;
    }
}

@media (max-width: 576px) {
    .testimonials-section {
        padding: 40px 0 30px;
    }

    .section-title {
        font-size: 24px;
        margin-bottom: 20px;
    }

    .featured-testimonial {
        margin-bottom: 40px;
    }

    .featured-testimonial-avatar {
        width: 60px;
        height: 60px;
    }

    .featured-testimonial-text {
        font-size: 16px;
        padding-right: 10px;
    }

    .featured-testimonial-content {
        padding: 20px;
    }

    .video-testimonials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .testimonial-avatar {
        width: 40px;
        height: 40px;
    }

    .testimonial-text {
        font-size: 14px;
        min-height: auto;
    }

    .testimonial-info h4 {
        font-size: 14px;
    }

    .testimonial-info p {
        font-size: 12px;
    }

    .testimonials-carousel {
        padding: 15px 0;
        margin-bottom: 40px;
    }

    .swiper-container {
        width: 100%;
        overflow: hidden !important;
        max-width: 100vw;
    }

    .swiper-slide {
        height: auto !important;
    }

    /* Fix video testimonials for mobile */
    .video-thumbnail {
        height: 200px;
    }

    .play-button {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* Extra small devices (phones, 375px and down) */
@media (max-width: 375px) {
    .testimonial-card {
        padding: 20px 15px;
    }

    .testimonial-quote {
        font-size: 20px;
    }

    .video-thumbnail {
        height: 180px;
    }

    .swiper-pagination-bullet {
        width: 8px !important;
        height: 8px !important;
    }
}
