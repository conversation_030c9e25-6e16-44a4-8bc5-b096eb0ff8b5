/* Header Styles */
.main-header {
    background-color: #fff;
    padding: 10px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.navbar-brand {
    margin-right: 0;
    padding: 0;
}

.navbar-nav {
    margin-right: 20px;
}

.nav-item {
    margin: 0 8px;
    display: flex;
    align-items: center;
}

.nav-link {
    color: #333;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.3s ease;
    padding: 8px 10px;
    white-space: nowrap;
}

.nav-link:hover, .nav-link.active {
    color: #00b5ad;
}

.header-buttons {
    display: flex;
    align-items: center;
    gap: 5px;
}

.header-buttons .btn {
    padding: 8px 10px;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.header-buttons .btn-primary {
    background-color: #00b5ad;
    border-color: #00b5ad;
}

.header-buttons .btn-primary:hover {
    background-color: #009d96;
    transform: translateY(-2px);
}

.header-buttons .btn-success {
    background-color: #25D366;
    border-color: #25D366;
}

.header-buttons .btn-success:hover {
    background-color: #20bd5b;
    transform: translateY(-2px);
}

/* Language Switcher LTR Support */
html[dir="ltr"] .navbar-nav {
    margin-right: 0;
    margin-left: 20px;
}

html[dir="ltr"] .language-selector {
    margin-right: 0;
    margin-left: 10px;
}

/* تعديل عرض العناصر ومرونتها */
@media (min-width: 992px) {
    .navbar-collapse {
        flex-grow: 1;
    }

    .navbar-nav {
        flex-wrap: nowrap;
        flex: 1;
        justify-content: flex-start;
    }

    /* جعل الهيدر يتكيف تلقائياً مع تغير طول النصوص */
    .container-fluid {
        max-width: 100%;
        padding-left: 0;
        padding-right: 0;
    }

    /* عند تغيير اللغة، تأكد من عدم تجاوز العناصر لعرض الشاشة */
    html[dir="ltr"] .nav-item {
        margin: 0 6px; /* هوامش أقل للغات غير العربية */
    }

    html[dir="ltr"] .nav-link {
        font-size: 12px; /* حجم خط أصغر للغات غير العربية */
    }

    html[dir="ltr"] .header-buttons .btn {
        padding: 8px 8px; /* تبطين أقل للأزرار في اللغات غير العربية */
        font-size: 12px;
    }
}

@media (max-width: 991px) {
    .navbar-nav {
        margin-right: 0;
        margin-top: 15px;
    }

    .header-buttons {
        margin-top: 15px;
        flex-direction: column;
        width: 100%;
        align-items: stretch;
    }

    .header-buttons .btn {
        margin: 5px 0;
        text-align: center;
    }
}

/* Add styling for the language selector */
.language-selector {
    display: flex;
    align-items: center;
    margin-left: 0;
    margin-right: 10px;
}

.language-selector .dropdown-toggle {
    border-radius: 20px;
    padding: 8px 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    border-color: #ddd;
    white-space: nowrap;
}

.language-selector .fas {
    font-size: 16px;
    color: #00b5ad;
}

.language-selector .dropdown-toggle:hover {
    background-color: #e9ecef;
    border-color: #ccc;
}

.language-selector .dropdown-menu {
    min-width: 150px;
    border-radius: 10px;
    padding: 8px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.language-selector .dropdown-item {
    padding: 8px 20px;
    font-size: 14px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.language-selector .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #00b5ad;
}

/* Make navbar more compact */
.navbar {
    padding: 0;
}

.navbar-collapse {
    justify-content: space-between;
}

/* Ensure proper vertical alignment */
.navbar-nav, .header-buttons {
    display: flex;
    align-items: center;
}

/* Hide Google translate widget */
#google_translate_element {
    height: 0;
    overflow: hidden;
    visibility: hidden;
    position: absolute;
}

/* Hide all Google Translate elements */
iframe.goog-te-banner-frame,
.goog-te-balloon-frame,
.goog-te-menu-frame,
.goog-te-menu-value,
.goog-te-status-bar {
    display: none !important;
}

/* Fix for translated page body positioning */
body {
    top: 0 !important;
    position: static !important;
}

/* Responsive fixes for language selector */
@media (max-width: 991px) {
    .language-selector {
        width: 100%;
        margin: 0 0 10px 0;
    }

    .language-selector .dropdown-toggle {
        width: 100%;
        justify-content: center;
    }

    .header-buttons .btn {
        width: 100%;
    }
}
