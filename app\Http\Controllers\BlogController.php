<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\Category;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    public function index(Request $request)
    {
        $categories = Category::where('is_active', true)
            ->orderBy('name')
            ->get();

        $query = Blog::where('is_published', true)
            ->whereHas('category', function ($q) {
                $q->where('is_active', true);
            })
            ->with('category');

        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category_id', $request->category);
        }

        $blogs = $query->orderBy('published_at', 'desc')
            ->paginate(9)
            ->withQueryString();

        return view('blog.index', compact('blogs', 'categories'));
    }

    public function show(Blog $blog)
    {
        if (!$blog->category->is_active) {
            abort(404);
        }

        if ($blog->tags) {
            if (is_string($blog->tags)) {
                $blog->tags = json_decode($blog->tags) ?: [];
            }
        } else {
            $blog->tags = [];
        }

        if ($blog->blog_highlights) {
            if (is_string($blog->blog_highlights)) {
                $blog->blog_highlights = json_decode($blog->blog_highlights) ?: [];
            }
        } else {
            $blog->blog_highlights = [];
        }

        $galleryImages = $blog->images()->orderBy('sort_order')->get();

        if ($galleryImages->isEmpty() && $blog->featured_image) {
            $virtualGallery = true;
        } else {
            $virtualGallery = false;
        }

        $similarBlogs = Blog::where('id', '!=', $blog->id)
            ->where('category_id', $blog->category_id)
            ->where('is_published', true)
            ->whereHas('category', function ($q) {
                $q->where('is_active', true);
            })
            ->latest()
            ->take(3)
            ->get();

        return view('blog.show', compact('blog', 'galleryImages', 'virtualGallery', 'similarBlogs'));
    }
}
