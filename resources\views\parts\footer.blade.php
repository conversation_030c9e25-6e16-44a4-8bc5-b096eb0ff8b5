<!-- Footer -->
<footer>
    @if(session('success'))
    <div class="toast-message" id="successToast">
        <i class="fas fa-check-circle ml-2"></i>
        {{ session('success') }}
    </div>
    @endif

    <div class="container-fluid">
        <div class="row">
            <!-- Contact Methods Section -->
            <div class="col-md-6">
                <h3 class="footer-heading">طرق التواصل معنا</h3>
                <div class="contact-methods-grid">
                    <div class="contact-method-group">
                        <a href="https://www.facebook.com/people/%D8%AE%D8%A8%D8%B1%D8%A7%D8%A1-%D9%84%D9%84%D8%A7%D8%B3%D8%AA%D8%B4%D8%A7%D8%B1%D8%A7%D8%AA-%D8%A7%D9%84%D8%A7%D9%82%D8%AA%D8%B5%D8%A7%D8%AF%D9%8A%D8%A9/61551783909820/" target="_blank" aria-label="Facebook">
                            <div class="contact-method">
                                <span class="contact-label">Facebook</span>
                                <i class="fab fa-facebook-f"></i>
                            </div>
                        </a>
                        <a href="https://x.com/Khobra_company" target="_blank" aria-label="Twitter">
                            <div class="contact-method">
                                <span class="contact-label">Twitter</span>
                                <i class="fab fa-twitter"></i>
                            </div>
                        </a>
                        <a href="https://www.instagram.com/" target="_blank" aria-label="Instagram">
                            <div class="contact-method">
                                <span class="contact-label">Instagram</span>
                                <i class="fab fa-instagram"></i>
                            </div>
                        </a>
                        <a href="https://www.linkedin.com/company/%D8%AE%D8%A8%D8%B1%D8%A7%D8%A1-%D9%84%D9%84%D8%A7%D8%B3%D8%AA%D8%B4%D8%A7%D8%B1%D8%A7%D8%AA-%D8%A7%D9%84%D8%A7%D9%82%D8%AA%D8%B5%D8%A7%D8%AF%D9%8A%D8%A9/" target="_blank" aria-label="LinkedIn">
                            <div class="contact-method">
                                <span class="contact-label">LinkedIn</span>
                                <i class="fab fa-linkedin-in"></i>
                            </div>
                        </a>
                    </div>
                    <div class="contact-method-group">
                        <a href="mailto:<EMAIL>" aria-label="Email">
                            <div class="contact-method">
                                <span class="contact-label"><EMAIL></span>
                                <i class="far fa-envelope"></i>
                            </div>
                        </a>
                        <a href="tel:+966569617288" aria-label="Phone">
                            <div class="contact-method">
                                <span class="contact-label">+966569617288</span>
                                <i class="fas fa-phone"></i>
                            </div>
                        </a>
                        <a href="#" aria-label="Mobile">
                            <div class="contact-method">
                                <span class="contact-label">+966569617288</span>
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                        </a>
                        <a href="https://wa.me/966569617288" aria-label="WhatsApp">
                            <div class="contact-method">
                                <span class="contact-label">+966569617288</span>
                                <i class="fab fa-whatsapp"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Contact Form Section -->
            <div class="col-md-6">
                <div class="contact-form-container">

                    <div class="footer-form">

                        <form class="contact-form" action="{{ route('contact.submit') }}" method="POST">
                            <h3 class="footer-form-heading">أرسل لنا رسالة</h3>
                            @csrf
                            <input type="hidden" name="email_to" value="<EMAIL>">
                            <div class="form-row">
                                <input type="text" name="name" class="form-control" placeholder="الاسم" required>
                            </div>
                            <div class="form-row phone-row">
                                <div class="input-group">
                                    <input type="text" name="phone" class="form-control" placeholder="مثال: 544902462" required>
                                    <div class="footer-country-code">966+</div>
                                </div>
                                <select name="inquiry_type" class="form-control" required>
                                    <option selected value="استشارة">استشارة</option>
                                    <option value="استفسار">استفسار</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <input type="text" name="city" class="form-control" placeholder="بأي مدينة مشروعك؟" required>
                            </div>
                            <div class="form-row">
                                <textarea name="message" class="form-control" rows="4" placeholder="الرسالة" required></textarea>
                            </div>
                            <div class="form-row">
                                <button type="submit" class="footer-btn-submit">إرسال</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vision Logo and Copyright -->
        <div class="vision-copyright">
            <div class="vision2030">
                <img src="{{ asset('assets/img/footer-logo.png') }}" alt="رؤية 2030" class="vision-logo">
                <!-- Social Media Links on Logo -->
                <div class="social-links">
                    <a href="https://www.facebook.com/people/%D8%AE%D8%A8%D8%B1%D8%A7%D8%A1-%D9%84%D9%84%D8%A7%D8%B3%D8%AA%D8%B4%D8%A7%D8%B1%D8%A7%D8%AA-%D8%A7%D9%84%D8%A7%D9%82%D8%AA%D8%B5%D8%A7%D8%AF%D9%8A%D8%A9/61551783909820/" target="_blank" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="https://x.com/Khobra_company" target="_blank" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="https://www.instagram.com/" target="_blank" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="https://www.linkedin.com/company/%D8%AE%D8%A8%D8%B1%D8%A7%D8%A1-%D9%84%D9%84%D8%A7%D8%B3%D8%AA%D8%B4%D8%A7%D8%B1%D8%A7%D8%AA-%D8%A7%D9%84%D8%A7%D9%82%D8%AA%D8%B5%D8%A7%D8%AF%D9%8A%D8%A9/" target="_blank" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
            <p class="footer-copy">جميع الحقوق محفوظة © {{ date('Y') }}</p>
        </div>
    </div>
</footer>

<!-- Chat Buttons -->
<div class="chat-btns">
    <a href="https://wa.me/966569617288" class="chat-btn whatsapp" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>
    <a href="https://www.facebook.com/people/%D8%AE%D8%A8%D8%B1%D8%A7%D8%A1-%D9%84%D9%84%D8%A7%D8%B3%D8%AA%D8%B4%D8%A7%D8%B1%D8%A7%D8%AA-%D8%A7%D9%84%D8%A7%D9%82%D8%AA%D8%B5%D8%A7%D8%AF%D9%8A%D8%A9/61551783909820/" class="chat-btn messenger" target="_blank">
        <i class="fab fa-facebook-messenger"></i>
    </a>
    <button type="button" class="chat-btn notification" onclick="window.toggleNotifications()">
        <i class="fas fa-bell"></i>
    </button>
</div>

<script>
    // Auto hide toast messages after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const toasts = document.querySelectorAll('.toast-message');

        toasts.forEach(function(toast) {
            setTimeout(function() {
                toast.classList.add('hide');
                setTimeout(function() {
                    toast.remove();
                }, 500);
            }, 5000);
        });
    });
</script>

<!-- Chatbase Chat Widget -->
<script>
(function(){
    if(!window.chatbase||window.chatbase("getState")!=="initialized"){
        window.chatbase=(...arguments)=>{if(!window.chatbase.q){window.chatbase.q=[]}window.chatbase.q.push(arguments)};
        window.chatbase=new Proxy(window.chatbase,{get(target,prop){if(prop==="q"){return target.q}return(...args)=>target(prop,...args)}})}

    // Configure the chatbot with custom settings
    window.chatbase('config', {
        chatbotId: 'lyxeppyYfOH0XOkJtC3Xn',
        domain: 'www.chatbase.co',
        title: 'المساعد الذكي 🤖',
        greeting: 'مرحباً! أنا المساعد الآلي الخاص بـخبراء. كيف يمكنني مساعدتك اليوم؟',
        theme: '#0077B6',
        buttonIcon: 'https://cdn-icons-png.flaticon.com/512/4712/4712010.png',
        buttonText: 'تحدث مع المساعد الآلي',
        position: 'right',
        width: '320px',
        height: '450px',
        opacity: 0.9,
        zIndex: 1000,
        showBranding: false
    });

    const onLoad=function(){
        const script=document.createElement("script");
        script.src="https://www.chatbase.co/embed.min.js";
        script.id="lyxeppyYfOH0XOkJtC3Xn";
        script.domain="www.chatbase.co";
        document.body.appendChild(script);

        // Add CSS to hide Chatbase branding
        setTimeout(function() {
            const style = document.createElement('style');
            style.textContent = `
                /* Hide Chatbase branding and footer */
                .chatbase-bubble .chatbase-footer,
                .chatbase-container .chatbase-footer,
                [class*="chatbase"] [class*="footer"],
                [class*="chatbase"] [class*="powered"],
                [class*="chatbase"] [class*="brand"],
                [class*="chatbase"] a[href*="chatbase"],
                iframe[src*="chatbase"] div[class*="footer"],
                iframe[src*="chatbase"] div[class*="brand"] {
                    display: none !important;
                    opacity: 0 !important;
                    visibility: hidden !important;
                    height: 0 !important;
                    padding: 0 !important;
                    margin: 0 !important;
                }

                /* Additional spacing to ensure no empty space */
                .chatbase-container .chatbase-messages {
                    padding-bottom: 0 !important;
                    margin-bottom: 0 !important;
                }

                /* Ensure chat container doesn't show branding at bottom */
                .chatbase-container {
                    padding-bottom: 0 !important;
                }
            `;
            document.head.appendChild(style);
        }, 2000); // Wait for chat widget to load
    };

    if(document.readyState==="complete"){
        onLoad()
    } else {
        window.addEventListener("load",onLoad)
    }
})();
</script>
