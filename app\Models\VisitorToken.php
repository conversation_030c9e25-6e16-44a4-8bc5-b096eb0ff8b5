<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VisitorToken extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'device_id',
        'fcm_token',
        'browser',
        'os',
        'ip_address',
        'user_agent',
        'last_active_at',
        'is_active',
        'is_subscribed',
        'topic',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'last_active_at' => 'datetime',
        'is_active' => 'boolean',
        'is_subscribed' => 'boolean',
    ];
}
