<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FirebaseNotificationService
{
    private $credentials;
    private $accessToken;
    private $projectId = 'obara-6a959';

    public function __construct()
    {
        $firebaseKey = file_get_contents(storage_path('app/firebase/obara-6a959-firebase-adminsdk-fbsvc-c60f66b04c.json'));
        $this->credentials = json_decode($firebaseKey, true);
    }

    public function sendNotification(
        string $fcmToken,
        string $title,
        string $body,
        string $link = '/test',
        array $options = [],
        array $data = []
    )
    {
        try {
            if (empty($fcmToken) || $fcmToken === 'null' || strlen($fcmToken) < 10) {
                Log::warning('Invalid FCM token provided, skipping notification', [
                    'token' => $fcmToken,
                    'title' => $title
                ]);
                return [
                    'success' => false,
                    'message' => 'Invalid FCM token provided'
                ];
            }

            Log::info('Starting to send notification', [
                'token' => substr($fcmToken, 0, 10) . '...',
                'title' => $title,
                'body' => $body,
                'link' => $link
            ]);

            if (!$this->accessToken) {
                Log::info('Getting new access token');
                $this->accessToken = $this->getAccessToken();
            }

            // إنشاء البيانات الأساسية للإشعار
            $payload = [
                'message' => [
                    'token' => $fcmToken,
                    'notification' => [
                        'title' => $title,
                        'body' => $body
                    ],
                    'webpush' => [
                        'headers' => [
                            'Urgency' => $options['priority'] ?? 'high'
                        ],
                        'notification' => [
                            'title' => $title,
                            'body' => $body,
                            'vibrate' => $options['vibrate'] ?? [100, 50, 100],
                            'requireInteraction' => $options['requireInteraction'] ?? true,
                            'dir' => 'rtl',
                            'lang' => 'ar',
                            'tag' => $options['tag'] ?? ('notification-' . time())
                        ],
                        'fcm_options' => [
                            'link' => $link
                        ]
                    ]
                ]
            ];

            // تحويل البيانات إلى نصوص (string) لتجنب خطأ FCM
            if (!empty($data)) {
                $stringData = [];
                foreach ($data as $key => $value) {
                    // تحويل أي قيمة إلى نص
                    $stringData[$key] = (string) $value;
                }
                $payload['message']['data'] = $stringData;
            }

            // إضافة خيارات إضافية إذا تم توفيرها
            if (isset($options['time_to_live'])) {
                $payload['message']['android']['ttl'] = $options['time_to_live'] . 's';
            }

            if (isset($options['direct_boot_ok'])) {
                $payload['message']['android']['direct_boot_ok'] = $options['direct_boot_ok'];
            }

            Log::info('Sending FCM request with payload', [
                'payload' => $payload
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json'
            ])->post("https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send", $payload);

            Log::info('FCM response received', [
                'status' => $response->status(),
                'body' => $response->json()
            ]);

            $responseData = $response->json();
            if (!$response->successful() && isset($responseData['error'])) {
                if (
                    strpos($responseData['error']['message'] ?? '', 'The registration token is not a valid') !== false ||
                    strpos($responseData['error']['message'] ?? '', 'expired') !== false
                ) {

                    Log::warning('FCM token is invalid or expired, should be removed', [
                        'token_prefix' => substr($fcmToken, 0, 10) . '...',
                        'error' => $responseData['error']['message'] ?? 'Unknown error'
                    ]);

                    // مثال: User::where('fcm_token', $fcmToken)->update(['fcm_token' => null]);
                }
            }

            return [
                'success' => $response->successful(),
                'message' => $response->json()
            ];
        } catch (\Exception $e) {
            Log::error('Error sending notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    public function sendNotificationToAdmins(string $title, string $body, string $link = '/admin/dashboard', array $options = [], array $data = [])
    {
        try {
            Log::info('Starting to send notifications to admins');

            $users = User::whereNotNull('fcm_token')
                ->where('fcm_token', '!=', 'null')
                ->get();

            $admins = $users->filter(function ($user) {
                return $user->hasRole('admin');
            });

            Log::info('Admin users found', [
                'total_users' => $users->count(),
                'admin_count' => $admins->count(),
                'admin_ids' => $admins->pluck('id')->toArray()
            ]);

            $results = [];
            foreach ($admins as $admin) {
                try {
                    if (empty($admin->fcm_token) || $admin->fcm_token === 'null' || strlen($admin->fcm_token) < 10) {
                        Log::info('Skipping admin with invalid token', [
                            'admin_id' => $admin->id,
                            'fcm_token' => $admin->fcm_token
                        ]);

                        $admin->fcm_token = null;
                        $admin->save();

                        $results[$admin->id] = [
                            'success' => false,
                            'error' => 'Invalid token'
                        ];
                        continue;
                    }

                    Log::info('Sending notification to admin', [
                        'admin_id' => $admin->id,
                        'fcm_token' => substr($admin->fcm_token, 0, 10) . '...'
                    ]);

                    // إضافة معرف المستخدم إلى البيانات
                    $adminData = $data;
                    $adminData['user_id'] = $admin->id;

                    // ضبط خيارات الأولوية العالية للمشرفين
                    $adminOptions = array_merge([
                        'priority' => 'high',
                        'requireInteraction' => true,
                        'tag' => 'admin-notification-' . time(),
                    ], $options);

                    $result = $this->sendNotification(
                        $admin->fcm_token,
                        $title,
                        $body,
                        $link,
                        $adminOptions,
                        $adminData
                    );

                    Log::info('Notification sent to admin', [
                        'admin_id' => $admin->id,
                        'result' => $result
                    ]);

                    if (
                        !$result['success'] &&
                        is_array($result['message']) &&
                        isset($result['message']['error']['message']) &&
                        (strpos($result['message']['error']['message'], 'The registration token is not a valid') !== false ||
                            strpos($result['message']['error']['message'], 'expired') !== false)
                    ) {

                        Log::warning('Removing invalid FCM token from admin', [
                            'admin_id' => $admin->id
                        ]);

                        $admin->fcm_token = null;
                        $admin->save();
                    }

                    $results[$admin->id] = $result;
                } catch (\Exception $e) {
                    Log::error("Failed to send notification to admin {$admin->id}", [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'fcm_token' => substr($admin->fcm_token, 0, 10) . '...'
                    ]);
                    $results[$admin->id] = [
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return [
                'success' => true,
                'results' => $results
            ];
        } catch (\Exception $e) {
            Log::error('Error sending notifications to admins', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    private function getAccessToken()
    {
        try {
            $now = time();
            $payload = [
                'iss' => $this->credentials['client_email'],
                'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
                'aud' => $this->credentials['token_uri'],
                'exp' => $now + 3600,
                'iat' => $now
            ];

            $jwt = $this->generateJWT($payload, $this->credentials['private_key']);

            $response = Http::asForm()->post($this->credentials['token_uri'], [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ]);

            if (!$response->successful()) {
                Log::error('Failed to get access token', [
                    'response' => $response->json()
                ]);
                throw new \Exception('Failed to get access token: ' . $response->body());
            }

            return $response->json()['access_token'];
        } catch (\Exception $e) {
            Log::error('Error getting access token', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function generateJWT($payload, $privateKey)
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
        $payload = json_encode($payload);

        $base64UrlHeader = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64UrlPayload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signatureInput = $base64UrlHeader . "." . $base64UrlPayload;
        openssl_sign($signatureInput, $signature, $privateKey, 'SHA256');
        $base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64UrlHeader . "." . $base64UrlPayload . "." . $base64UrlSignature;
    }
}
