<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Blog extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'featured_image',
        'tags',
        'blog_highlights',
        'contact_info',
        'is_published',
        'published_at',
        'category_id'
    ];

    protected $casts = [
        'tags' => 'array',
        'blog_highlights' => 'array',
        'is_published' => 'boolean',
        'published_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($blog) {
            if (empty($blog->slug)) {
                $blog->slug = Str::slug($blog->title);
            }
        });
    }

    /**
     * Get the gallery images for the blog.
     */
    public function images()
    {
        return $this->hasMany(BlogImage::class)->orderBy('sort_order');
    }

    /**
     * Get the primary gallery image for the blog.
     */
    public function primaryImage()
    {
        return $this->hasOne(BlogImage::class)->where('is_primary', true);
    }

    /**
     * Get the category that owns the blog.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }
}
