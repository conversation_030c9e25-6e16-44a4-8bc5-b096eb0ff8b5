/* =============================================
   Thank You Page Styles - Modern & Clean Design
   ============================================= */

:root {
    --success-color: #2dd1ac;
    --success-color-light: #e1f9f3;
    --primary-color: #2dd1ac;
    --primary-hover: #2dd1ac;
    --text-primary: #222b45;
    --text-secondary: #6b7a99;
    --background-light: #f7f9fc;
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    --white: #ffffff;
}

.thank-you-section {
    min-height: 85vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-light);
    padding: 4rem 1rem;
    position: relative;
    overflow: hidden;
}

.thank-you-section::before {
    content: "";
    position: absolute;
    top: -5%;
    left: -5%;
    width: 40%;
    height: 40%;
    background: var(--success-color-light);
    border-radius: 50%;
    opacity: 0.6;
    z-index: 0;
}

.thank-you-section::after {
    content: "";
    position: absolute;
    bottom: -10%;
    right: -10%;
    width: 60%;
    height: 60%;
    background: var(--success-color-light);
    border-radius: 50%;
    opacity: 0.4;
    z-index: 0;
}

.thank-you-card {
    position: relative;
    z-index: 10;
    max-width: 550px;
    width: 100%;
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--card-shadow);
    padding: 3.5rem 2.5rem;
    text-align: center;
    transition: transform 0.3s ease;
    animation: cardEnter 0.8s ease-out;
}

.thank-you-card:hover {
    transform: translateY(-5px);
}

.success-icon {
    width: 110px;
    height: 110px;
    margin: 0 auto 2rem;
    position: relative;
}

.success-icon svg {
    width: 100%;
    height: 100%;
    color: var(--success-color);
    filter: drop-shadow(0 5px 15px rgba(45, 209, 172, 0.4));
    animation: iconPulse 2s infinite ease-in-out;
}

.thank-you-title {
    color: var(--text-primary);
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.8s ease-out 0.3s both;
}

.thank-you-message {
    color: var(--text-secondary);
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    animation: fadeIn 0.8s ease-out 0.6s both;
}

.thank-you-message p + p {
    margin-top: 0.75rem;
}

.home-button {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    font-size: 1.125rem;
    font-weight: 600;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    box-shadow: 0 8px 15px rgba(23, 105, 255, 0.2);
    transition: all 0.3s ease;
    animation: fadeIn 0.8s ease-out 0.9s both;
    position: relative;
    overflow: hidden;
}

.home-button:hover {
    background: var(--primary-hover);
    box-shadow: 0 12px 20px rgba(23, 105, 255, 0.3);
    transform: translateY(-3px);
}

.home-button:active {
    transform: translateY(0);
}

.home-button::after {
    content: "";
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    width: 100%;
    height: 100%;
    top: 0;
    left: -100%;
    border-radius: 50px;
    transition: all 0.4s ease;
}

.home-button:hover::after {
    left: 100%;
}

/* Animations */
@keyframes cardEnter {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes iconPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(15px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Confetti Animation */
.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: var(--success-color);
    border-radius: 0;
    opacity: 0.8;
    animation: confetti 5s ease-in-out infinite;
}

.confetti:nth-child(1) {
    top: 10%;
    left: 10%;
    background: #ff7a7a;
    transform: rotate(15deg);
    animation-delay: 0s;
}

.confetti:nth-child(2) {
    top: 20%;
    left: 80%;
    background: #ffcf7a;
    transform: rotate(45deg);
    animation-delay: 0.4s;
}

.confetti:nth-child(3) {
    top: 30%;
    left: 20%;
    background: #85d7ff;
    transform: rotate(30deg);
    animation-delay: 0.8s;
}

.confetti:nth-child(4) {
    top: 60%;
    left: 85%;
    background: #c28aff;
    transform: rotate(60deg);
    animation-delay: 1.2s;
}

.confetti:nth-child(5) {
    top: 70%;
    left: 40%;
    background: #ff99d6;
    transform: rotate(15deg);
    animation-delay: 1.6s;
}

.confetti:nth-child(6) {
    top: 5%;
    left: 50%;
    background: #91ff9e;
    transform: rotate(45deg);
    animation-delay: 2s;
}

.confetti:nth-child(7) {
    top: 85%;
    left: 15%;
    background: #ffde59;
    transform: rotate(30deg);
    animation-delay: 2.4s;
}

.confetti:nth-child(8) {
    top: 50%;
    left: 5%;
    background: #b0eaff;
    transform: rotate(20deg);
    animation-delay: 2.8s;
}

@keyframes confetti {
    0% {
        transform: rotate(0deg) translateY(0) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: rotate(180deg) translateY(-100px) scale(1.5);
        opacity: 1;
    }
    100% {
        transform: rotate(360deg) translateY(0) scale(1);
        opacity: 0.8;
    }
}

/* Responsive Styles */
@media (max-width: 767px) {
    .thank-you-section {
        padding: 3rem 1rem;
    }

    .thank-you-card {
        padding: 2.5rem 1.5rem;
    }

    .success-icon {
        width: 90px;
        height: 90px;
        margin-bottom: 1.5rem;
    }

    .thank-you-title {
        font-size: 1.75rem;
        margin-bottom: 1rem;
    }

    .thank-you-message {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .home-button {
        font-size: 1rem;
        padding: 0.875rem 2rem;
    }
}

@media (max-width: 480px) {
    .thank-you-card {
        padding: 2rem 1.25rem;
    }

    .success-icon {
        width: 80px;
        height: 80px;
    }

    .thank-you-title {
        font-size: 1.5rem;
    }

    .home-button {
        width: 100%;
    }
}
