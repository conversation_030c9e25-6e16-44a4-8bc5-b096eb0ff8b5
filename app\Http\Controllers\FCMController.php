<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class FCMController extends Controller
{
    /**
     * Update the FCM token for the authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateToken(Request $request)
    {
        try {
            $request->validate([
                'token' => 'required|string'
            ]);

            $token = $request->token;

            // تحقق من صحة التوكن - إذا كان فارغًا أو 'null' أو 'undefined'، اجعله NULL
            if (empty($token) || $token === 'null' || $token === 'undefined' || strlen($token) < 10) {
                $token = null;
                Log::info('Setting token to NULL due to invalid value', [
                    'original_token' => $request->token
                ]);
            }

            $user = Auth::user();

            if ($user) {
                // تأكد من وجود المستخدم وتحديث التوكن
                $user = User::find($user->id);
                if ($user) {
                    $user->fcm_token = $token;
                    $user->save();

                    Log::info('FCM token updated for user', [
                        'user_id' => $user->id,
                        'token_status' => $token ? 'valid_token' : 'cleared_token',
                        'token_preview' => $token ? substr($token, 0, 10) . '...' : 'NULL'
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'FCM token updated successfully',
                        'token_status' => $token ? 'saved' : 'cleared'
                    ]);
                } else {
                    Log::error('User found in Auth but not in database');
                    return response()->json([
                        'success' => false,
                        'message' => 'User not found in database'
                    ], 404);
                }
            } else {
                Log::warning('No authenticated user found when updating FCM token');
                return response()->json([
                    'success' => false,
                    'message' => 'No authenticated user found'
                ], 401);
            }
        } catch (\Exception $e) {
            Log::error('Error updating FCM token', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id() ?? 'unauthenticated'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update FCM token: ' . $e->getMessage()
            ], 500);
        }
    }
}
