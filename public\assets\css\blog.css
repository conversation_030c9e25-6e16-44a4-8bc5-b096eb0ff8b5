 جدا/* General Link Styles */
a {
    text-decoration: none;
}

/* Hero Section */
.hero-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.4)),
        url("../img/blog/photo-1604594849809-dfedbc827105.jfif");
    background-size: cover;
    background-position: center;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    margin-bottom: 60px;
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 1s ease-out;
}

.hero-content p {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Blog Section */
.blog-section {
    padding: 60px 0;
    background-color: #f8f9fa;
}

/* Blog Item - Modern Redesign */
.blog-item {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    margin-bottom: 35px;
    height: 100%;
    background-color: #fff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
    border: 1px solid rgba(0, 181, 173, 0.1);
}

.blog-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 181, 173, 0.15);
}

.blog-img-container {
    position: relative;
    width: 100%;
    height: 240px;
    overflow: hidden;
    min-height: 240px;
}

.blog-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.blog-item:hover img {
    transform: scale(1.05);
}

.blog-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 10px 18px;
    background: linear-gradient(135deg, #00b5ad 0%, #008c85 100%);
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    border-radius: 30px;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(0, 181, 173, 0.25);
}

.blog-type {
    position: absolute;
    bottom: 15px;
    left: 15px;
    padding: 7px 16px;
    background-color: rgba(0, 0, 0, 0.75);
    color: #fff;
    font-size: 13px;
    font-weight: 500;
    border-radius: 20px;
    z-index: 2;
    backdrop-filter: blur(4px);
    transition: background-color 0.3s ease;
}

.blog-content {
    padding: 28px;
    border-top: none;
}

.blog-content h3 {
    color: #333;
    font-size: 21px;
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 700;
    text-align: right;
    line-height: 1.4;
}

.blog-content h3 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.blog-content h3 a:hover {
    color: #00b5ad;
    text-decoration: none;
}

.blog-description {
    color: #666;
    font-size: 15px;
    margin-bottom: 20px;
    line-height: 1.7;
    text-align: right;
    height: 76px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.blog-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 18px;
    border-top: 1px solid rgba(0, 181, 173, 0.1);
}

.blog-date {
    color: #777;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.blog-date i {
    margin-left: 8px;
    color: #00b5ad;
}

.read-more {
    color: #00b5ad;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    transition: color 0.3s ease, transform 0.3s ease;
    text-decoration: none;
}

.read-more:hover {
    color: #008c85;
    transform: translateX(-4px);
    text-decoration: none;
}

.read-more i {
    margin-right: 8px;
    transition: transform 0.3s ease;
}

.read-more:hover i {
    transform: translateX(-4px);
}

/* Blog Detail Page */
.blog-detail {
    padding: 50px 0;
    background-color: #f8f9fa;
}

.blog-detail-container {
    background-color: #fff;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.05);
}

.blog-detail-image {
    width: 100%;
    max-height: 500px;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.blog-detail-meta {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
}

.blog-detail-meta .meta-item {
    display: flex;
    align-items: center;
    margin-left: 20px;
    margin-bottom: 5px;
    margin-top: 5px;
    color: #666;
    font-size: 14px;
}

.blog-detail-meta .meta-item i {
    margin-left: 8px;
    color: #00b5ad;
    font-size: 16px;
}

.blog-detail-content {
    line-height: 1.8;
    color: #333;
    font-size: 16px;
    margin-bottom: 40px;
    text-align: right;
    max-width: 100%;
    overflow: hidden;
}

.blog-detail-content h2,
.blog-detail-content h3 {
    margin-top: 30px;
    margin-bottom: 15px;
    color: #222;
    font-weight: 600;
}

.blog-detail-content p {
    margin-bottom: 20px;
    max-width: 100%;
}

.blog-detail-content img {
    max-width: 100%;
    height: auto !important;
    max-height: 400px;
    object-fit: contain;
    display: block;
    margin: 20px auto;
    background-color: #f8f9fa;
}

.blog-detail-content figure {
    max-width: 100% !important;
    margin: 20px auto;
}

.blog-detail-content figure img {
    max-width: 100% !important;
    height: auto !important;
}

.blog-detail-content iframe {
    max-width: 100%;
    margin: 20px auto;
}

.blog-share {
    display: flex;
    align-items: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}

.blog-share-label {
    margin-left: 15px;
    font-weight: 600;
    color: #333;
}

.blog-share-buttons {
    display: flex;
}

.blog-share-buttons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #fff;
    margin-left: 10px;
    transition: all 0.3s ease;
}

.blog-share-buttons a:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.blog-share-buttons .facebook {
    background-color: #3b5998;
}

.blog-share-buttons .twitter {
    background-color: #1da1f2;
}

.blog-share-buttons .linkedin {
    background-color: #0077b5;
}

/* Investment Box */
.investment-box {
    background: linear-gradient(to right, #f8f9fa, #fff);
    border-radius: 12px;
    padding: 30px;
    margin: 30px 0;
    border-right: 4px solid #00b5ad;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.investment-box::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 80px;
    height: 80px;
    background-color: #00b5ad;
    opacity: 0.1;
    border-radius: 0 0 0 100%;
}

.investment-box h3 {
    color: #00b5ad;
    margin-bottom: 20px;
    font-weight: 700;
    font-size: 24px;
    position: relative;
    display: inline-block;
}

.investment-box h3::after {
    content: "";
    position: absolute;
    bottom: -8px;
    right: 0;
    width: 40px;
    height: 3px;
    background-color: #00b5ad;
}

.investment-details {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    row-gap: 20px;
}

.investment-detail {
    flex: 1 0 48%;
    margin-bottom: 0;
    min-width: 200px;
    margin-left: 4%;
}

.investment-detail:nth-child(2n) {
    margin-left: 0;
}

.investment-detail strong {
    display: block;
    color: #333;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
}

.investment-detail span {
    color: #666;
    font-size: 15px;
    background-color: rgba(0, 181, 173, 0.05);
    padding: 8px 12px;
    border-radius: 6px;
    display: inline-block;
}

.investment-cta {
    text-align: center;
    margin-top: 30px;
}

.investment-button {
    display: inline-block;
    background: linear-gradient(135deg, #00b5ad 0%, #008c85 100%);
    color: #fff;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(0, 181, 173, 0.3);
}

.investment-button:hover {
    background: linear-gradient(135deg, #008c85 0%, #00b5ad 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 181, 173, 0.4);
    color: #fff;
    text-decoration: none;
}

/* Investment Grid */
.investment-grid {
    padding: 70px 0;
    position: relative;
    background-color: #f9fafb;
    background-image: linear-gradient(
            rgba(249, 250, 251, 0.97),
            rgba(249, 250, 251, 0.97)
        ),
        url("../img/patterns/pattern-dots.png");
    background-repeat: repeat;
}

.investment-grid::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.03;
    z-index: 0;
}

.investment-grid .container {
    position: relative;
    z-index: 1;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 40px;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 181, 173, 0.08);
}

.investment-grid .row {
    margin-top: 30px;
}

.investment-grid .section-title {
    text-align: center;
    margin-bottom: 40px;
}

.investment-grid .section-title h2 {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.investment-grid .section-title h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #00b5ad 0%, #008c85 100%);
}

.investment-grid .section-title p {
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

/* Modern Filter Redesign */
.investment-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 30px;
}

.investment-filter {
    padding: 10px 22px;
    background-color: #f8f9fa;
    border: 1px solid rgba(0, 181, 173, 0.15);
    border-radius: 30px;
    color: #555;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.investment-filter::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 181, 173, 0.1) 0%,
        rgba(0, 140, 133, 0.05) 100%
    );
    transition: width 0.3s ease;
    z-index: -1;
}

.investment-filter:hover::before {
    width: 100%;
}

.investment-filter:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 181, 173, 0.15);
    border-color: rgba(0, 181, 173, 0.3);
    color: #333;
}

.investment-filter.active {
    background: linear-gradient(135deg, #00b5ad 0%, #008c85 100%);
    color: #fff;
    border-color: transparent;
    box-shadow: 0 5px 15px rgba(0, 181, 173, 0.25);
}

.investment-filter.active:hover {
    transform: translateY(-3px);
}

/* Pagination Modern Redesign */
.pagination-wrapper {
    margin-top: 60px;
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    gap: 8px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.pagination li {
    margin: 0;
}

.pagination li a,
.pagination li span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    background-color: #fff;
    border: 1px solid rgba(0, 181, 173, 0.15);
    color: #555;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 15px;
    overflow: hidden;
    position: relative;
}

.pagination li.active span,
.pagination li.active a {
    background: linear-gradient(135deg, #00b5ad 0%, #008c85 100%);
    border-color: transparent;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(0, 181, 173, 0.25);
}

.pagination li a:hover {
    background-color: rgba(0, 181, 173, 0.05);
    border-color: rgba(0, 181, 173, 0.3);
    color: #00b5ad;
    transform: translateY(-2px);
}

/* إخفاء أيقونات SVG واستبدالها */
.pagination li:first-child a,
.pagination li:last-child a {
    font-size: 0;
}

.pagination li:first-child a::before,
.pagination li:last-child a::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-size: 18px;
    line-height: 42px;
    text-align: center;
}

.pagination li:first-child a::before {
    content: "‹";
}

.pagination li:last-child a::before {
    content: "›";
}

/* إخفاء كل الـ SVG */
.pagination svg {
    display: none !important;
}

/* إخفاء نصوص السابق/التالي */
.pagination-wrapper .flex.justify-between {
    display: none;
}

/* إخفاء نص النتائج */
.pagination-wrapper p {
    display: none;
}

/* Chat Buttons */
.chat-btns {
    position: fixed;
    bottom: 30px;
    left: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 100;
}

.chat-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.chat-btn.whatsapp {
    background-color: #25d366;
}

.chat-btn.messenger {
    background-color: #0084ff;
}

.chat-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Empty State Modern Redesign */
.empty-state {
    text-align: center;
    padding: 70px 30px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 181, 173, 0.1);
}

.empty-state i {
    font-size: 65px;
    color: rgba(0, 181, 173, 0.3);
    margin-bottom: 25px;
}

.empty-state h3 {
    color: #444;
    font-size: 26px;
    margin-bottom: 15px;
    font-weight: 600;
}

.empty-state p {
    color: #777;
    max-width: 500px;
    margin: 0 auto;
    font-size: 16px;
}

/* Gallery Section */
.gallery-section {
    margin: 40px 0;
    position: relative;
}

.gallery-top {
    height: 500px;
    width: 100%;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.gallery-top .swiper-slide {
    position: relative;
    height: 100%;
}

.gallery-top .swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Hide gallery caption by default when there's only one slide */
.gallery-top .swiper-wrapper:only-child .gallery-caption,
.gallery-top .swiper-wrapper.single-slide .gallery-caption,
.gallery-top.single-image .gallery-caption {
    display: none;
}

/* Show gallery caption when there are multiple slides */
.gallery-top
    .swiper-wrapper:not(.single-slide):not(:only-child)
    .gallery-caption,
.gallery-top:not(.single-image) .gallery-caption {
    display: block;
}

/* Hide navigation arrows for single image galleries */
.gallery-top.single-image .swiper-button-next,
.gallery-top.single-image .swiper-button-prev,
.gallery-top .swiper-wrapper.single-slide ~ .swiper-button-next,
.gallery-top .swiper-wrapper.single-slide ~ .swiper-button-prev,
.gallery-main-slider.single-image .custom-nav-button,
.gallery-main-slider .swiper-wrapper.single-slide ~ .custom-nav-button {
    display: none !important;
}

/* Hide caption for single image galleries */
.gallery-main-slider.single-image .gallery-caption,
.gallery-main-slider .swiper-wrapper.single-slide .gallery-caption {
    display: none !important;
}

.gallery-top .gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 15px;
    text-align: center;
}

.gallery-thumbs {
    height: 100px;
    box-sizing: border-box;
    padding: 10px 0;
}

.gallery-thumbs .swiper-slide {
    height: 100%;
    opacity: 0.4;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.gallery-thumbs .swiper-slide-thumb-active {
    opacity: 1;
}

.gallery-thumbs .swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.gallery-thumbs .swiper-slide-thumb-active img {
    border: 2px solid #00b5ad;
}

/* Gallery Navigation Buttons */
.gallery-top .swiper-button-next,
.gallery-top .swiper-button-prev {
    color: #fff;
    background: rgba(0, 0, 0, 0.6);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* إزالة النص تماماً من الأزرار */
.gallery-top .swiper-button-next::after,
.gallery-top .swiper-button-prev::after {
    content: "" !important;
    width: 15px;
    height: 15px;
    border-top: 3px solid #fff;
    border-right: 3px solid #fff;
    background-image: none !important;
    font-size: 0 !important;
    text-indent: -9999px;
}

/* عكس اتجاه الأسهم للتصميم العربي */
.gallery-top .swiper-button-next::after {
    transform: rotate(-135deg); /* السهم للخلف (يمين في RTL) */
    margin-left: 5px;
}

.gallery-top .swiper-button-prev::after {
    transform: rotate(45deg); /* السهم للأمام (يسار في RTL) */
    margin-right: 5px;
}

.gallery-top .swiper-button-next:hover,
.gallery-top .swiper-button-prev:hover {
    background: rgba(0, 181, 173, 0.9);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.gallery-top .swiper-button-next:hover::after,
.gallery-top .swiper-button-prev:hover::after {
    border-color: #fff;
}

.gallery-top .swiper-button-disabled {
    opacity: 0.35;
    pointer-events: none;
}

/* استخدام !important للتأكد من إخفاء النص بالكامل */
.swiper-button-next span,
.swiper-button-prev span {
    display: none !important;
}

@media (max-width: 768px) {
    .gallery-top {
        height: 400px;
    }

    .gallery-thumbs {
        height: 80px;
    }

    .gallery-captions {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 576px) {
    .gallery-top {
        height: 300px;
    }

    .gallery-thumbs {
        height: 60px;
    }

    .gallery-captions {
        grid-template-columns: 1fr;
    }

    .gallery-caption img {
        height: 180px;
    }
}

/* Gallery Captions Section */
.gallery-captions {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.gallery-caption {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.gallery-caption:hover {
    transform: translateY(-5px);
}

.gallery-caption img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-caption > div {
    padding: 15px;
}

.gallery-caption .text-muted {
    font-size: 14px;
    margin-bottom: 8px;
}

.gallery-caption p {
    margin: 0;
    color: #333;
    font-size: 16px;
    line-height: 1.5;
}

/* Responsive Gallery */
@media (max-width: 768px) {
    .gallery-top {
        height: 400px;
    }

    .gallery-thumbs {
        height: 80px;
    }

    .gallery-captions {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 576px) {
    .gallery-top {
        height: 300px;
    }

    .gallery-thumbs {
        height: 60px;
    }

    .gallery-captions {
        grid-template-columns: 1fr;
    }

    .gallery-caption img {
        height: 180px;
    }
}

/* Tags Section */
.blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
}

.blog-tag {
    background-color: #f8f9fa;
    color: #333;
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 1px solid #eee;
    white-space: nowrap;
}

.blog-tag:hover {
    background-color: #00b5ad;
    color: #fff;
    border-color: #00b5ad;
}

/* Improved Responsive Design */
@media (max-width: 1200px) {
    .blog-detail-container {
        padding: 25px;
    }

    .gallery-captions {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 991px) {
    .blog-img-container {
        min-height: 220px;
    }

    .blog-content {
        padding: 25px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 400px;
    }

    .hero-content {
        padding: 100px 20px;
    }

    .hero-content h1 {
        font-size: 32px;
    }

    .blog-detail-container {
        padding: 22px;
    }

    .gallery-captions {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .blog-img-container {
        min-height: 200px;
    }

    .gallery-caption img {
        height: 200px;
    }

    .investment-filters {
        gap: 10px;
    }

    .investment-filter {
        padding: 8px 18px;
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .hero-section {
        height: 300px;
    }

    .hero-content {
        padding: 60px 15px;
    }

    .hero-content h1 {
        font-size: 24px;
    }

    .blog-detail-container {
        padding: 20px;
    }

    .gallery-captions {
        grid-template-columns: 1fr;
    }

    .blog-img-container {
        min-height: 180px;
    }

    .gallery-caption img {
        height: 180px;
    }

    .blog-tag {
        font-size: 12px;
        padding: 6px 12px;
    }

    .investment-box {
        padding: 15px;
    }

    .investment-detail {
        min-width: 100%;
    }
}

@media (max-width: 375px) {
    .hero-section {
        height: 250px;
    }

    .hero-content h1 {
        font-size: 20px;
    }

    .blog-detail-container {
        padding: 10px;
    }
}

/* Tags Section */
.blog-detail-container .d-flex.flex-wrap.gap-2 {
    display: flex;
    flex-wrap: wrap;
    gap: 8px !important;
    margin: 0 -4px;
}

.blog-detail-container .badge {
    margin: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 30px;
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #eee;
    white-space: normal;
    text-align: center;
    line-height: 1.4;
    transition: all 0.3s ease;
}

.blog-detail-container .badge:hover {
    background-color: #00b5ad;
    color: #fff;
    border-color: #00b5ad;
}

@media (max-width: 768px) {
    .blog-detail-container .d-flex.flex-wrap.gap-2 {
        gap: 6px !important;
        margin: 0 -3px;
    }

    .blog-detail-container .badge {
        margin: 3px;
        padding: 6px 12px;
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .blog-detail-container .d-flex.flex-wrap.gap-2 {
        gap: 4px !important;
        margin: 0 -2px;
    }

    .blog-detail-container .badge {
        margin: 2px;
        padding: 5px 10px;
        font-size: 12px;
    }
}

/* أسهم التنقل المخصصة */
.custom-arrow {
    position: relative;
    width: 15px;
    height: 15px;
}

.swiper-button-next .custom-arrow:after,
.swiper-button-prev .custom-arrow:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-top: 3px solid #fff;
    border-right: 3px solid #fff;
}

.swiper-button-next .custom-arrow:after {
    transform: rotate(-135deg); /* السهم للخلف (يمين في RTL) */
}

.swiper-button-prev .custom-arrow:after {
    transform: rotate(45deg); /* السهم للأمام (يسار في RTL) */
}

/* Responsive adjustments for investment grid container */
@media (max-width: 991px) {
    .investment-grid {
        padding: 60px 0;
    }

    .investment-grid .container {
        padding: 30px;
        border-radius: 20px;
    }
}

@media (max-width: 768px) {
    .investment-grid {
        padding: 50px 0;
    }

    .investment-grid .container {
        padding: 25px;
        border-radius: 16px;
    }

    .investment-grid .row {
        margin-top: 20px;
    }

    .gallery-top .swiper-button-next,
    .gallery-top .swiper-button-prev {
        width: 40px;
        height: 40px;
    }

    .gallery-top .swiper-button-next::after,
    .gallery-top .swiper-button-prev::after {
        width: 12px;
        height: 12px;
        border-width: 2.5px;
    }
}

@media (max-width: 576px) {
    .investment-grid {
        padding: 40px 0;
    }

    .investment-grid .container {
        padding: 20px 15px;
        border-radius: 12px;
    }

    .gallery-top .swiper-button-next,
    .gallery-top .swiper-button-prev {
        width: 35px;
        height: 35px;
    }

    .gallery-top .swiper-button-next::after,
    .gallery-top .swiper-button-prev::after {
        width: 10px;
        height: 10px;
        border-width: 2px;
    }
}

/* Modern Container Design */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Decorative Elements for Modern Container */
.investment-grid::after {
    content: "";
    position: absolute;
    top: 60px;
    right: 5%;
    width: 150px;
    height: 150px;
    background: linear-gradient(
        135deg,
        rgba(0, 181, 173, 0.15) 0%,
        rgba(0, 140, 133, 0.05) 100%
    );
    border-radius: 50%;
    filter: blur(40px);
    z-index: 0;
}

.investment-grid::before {
    content: "";
    position: absolute;
    bottom: 80px;
    left: 5%;
    width: 120px;
    height: 120px;
    background: linear-gradient(
        135deg,
        rgba(0, 181, 173, 0.1) 0%,
        rgba(0, 140, 133, 0.03) 100%
    );
    border-radius: 50%;
    filter: blur(30px);
    z-index: 0;
    opacity: 0.8;
}

.investment-grid .container::before {
    content: "";
    position: absolute;
    top: -15px;
    right: 30px;
    width: 30px;
    height: 30px;
    background-color: rgba(0, 181, 173, 0.6);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.2;
}

.investment-grid .container::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 40px;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 181, 173, 0.6);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.15;
}

/* Modern Filter Redesign - Dropdowns */
.filter-dropdown {
    position: relative;
    min-width: 220px;
}

.filter-dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 12px 20px;
    background-color: #fff;
    border: 1px solid rgba(0, 181, 173, 0.15);
    border-radius: 12px;
    color: #444;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.filter-dropdown-toggle:hover {
    border-color: rgba(0, 181, 173, 0.4);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
}

.filter-dropdown-toggle i {
    margin-left: 8px;
    color: #00b5ad;
    transition: transform 0.3s ease;
}

.filter-dropdown.active .filter-dropdown-toggle i {
    transform: rotate(180deg);
}

.filter-dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 100%;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
    border: 1px solid rgba(0, 181, 173, 0.1);
    visibility: hidden;
}

.filter-dropdown.active .filter-dropdown-menu {
    max-height: 300px;
    opacity: 1;
    visibility: visible;
}

.filter-dropdown-item {
    display: block;
    padding: 10px 20px;
    color: #555;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s ease;
    text-align: right;
}

.filter-dropdown-item:hover {
    background-color: rgba(0, 181, 173, 0.05);
    color: #00b5ad;
}

.filter-dropdown-item.active {
    background-color: rgba(0, 181, 173, 0.08);
    color: #00b5ad;
    font-weight: 600;
}

/* Similar Investments Section */
.similar-investments {
    padding: 40px 0;
    margin-top: 40px;
}

.similar-investments .section-title {
    text-align: center;
    margin-bottom: 40px;
}

.similar-investments .section-title h2 {
    font-size: 32px;
    font-weight: 700;
    color: #00b5ad;
    margin-bottom: 0;
}

.similar-investment-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid #eee;
}

.similar-investment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.similar-investment-card .card-image {
    position: relative;
    width: 100%;
    height: 240px;
    overflow: hidden;
}

.similar-investment-card .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.similar-investment-card:hover .card-image img {
    transform: scale(1.05);
}

.similar-investment-card .investment-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 16px;
    background: #00b5ad;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    border-radius: 20px;
    z-index: 2;
}

.similar-investment-card .category-badge {
    position: absolute;
    bottom: 15px;
    left: 15px;
    padding: 6px 14px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    font-size: 13px;
    font-weight: 500;
    border-radius: 20px;
    z-index: 2;
}

.similar-investment-card .card-content {
    padding: 20px;
}

.similar-investment-card .card-title {
    color: #333;
    font-size: 18px;
    margin: 0 0 12px 0;
    font-weight: 600;
    text-align: right;
    line-height: 1.4;
}

.similar-investment-card .card-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.similar-investment-card .card-title a:hover {
    color: #00b5ad;
}

.similar-investment-card .card-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.6;
    text-align: right;
    height: 67px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.similar-investment-card .card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-top: 1px solid #eee;
    background-color: #fafafa;
}

.similar-investment-card .publish-date {
    color: #777;
    font-size: 13px;
    display: flex;
    align-items: center;
}

.similar-investment-card .publish-date i {
    margin-left: 6px;
    color: #00b5ad;
}

.similar-investment-card .view-details-btn {
    color: #00b5ad;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.similar-investment-card .view-details-btn:hover {
    color: #008c85;
}

.similar-investment-card .view-details-btn i {
    margin-right: 6px;
    transition: transform 0.3s ease;
}

.similar-investment-card .view-details-btn:hover i {
    transform: translateX(-4px);
}

@media (max-width: 768px) {
    .similar-investments {
        padding: 30px 0;
    }

    .similar-investment-card .card-image {
        height: 200px;
    }
}

@media (max-width: 576px) {
    .similar-investments {
        padding: 20px 0;
    }

    .similar-investment-card .card-image {
        height: 180px;
    }
}
