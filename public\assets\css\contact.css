/* Variables */
:root {
    --primary-color: #00b5ad;
    --primary-dark: #009d96;
    --secondary-color: #2c3e50;
    --text-color: #333;
    --text-light: #666;
    --white: #fff;
    --light-gray: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Hero Section */
.hero-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.4)),
        url("../img/contact/contact-us.jpg");
    background-size: cover;
    background-position: center;
    height: 600px;
    display: flex;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 0 20px;
    margin-top: -50px;
    animation: fadeIn 1s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content h1 {
    color: var(--white);
    font-size: 64px;
    font-weight: 800;
    margin-bottom: 25px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-content p {
    color: var(--white);
    font-size: 24px;
    line-height: 1.8;
    opacity: 0.95;
    max-width: 800px;
    margin: 0 auto;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Contact Form Section */
.contact-form-section {
    position: relative;
    margin-top: -50px;
    padding-bottom: 80px;
}

.contact-form-wrapper {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 40px;
    position: relative;
    z-index: 3;
    transition: var(--transition);
}

.contact-form-wrapper:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.form-group {
    margin-bottom: 20px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #eee;
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
    background-color: var(--white);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 181, 173, 0.1);
}

.phone-input {
    position: relative;
}

.country-code {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 16px;
    padding: 0 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.phone-input .form-control {
    padding-left: 65px;
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
    padding-right: 45px;
}

textarea.form-control {
    resize: none;
    height: 120px;
}

.btn-submit {
    background-color: var(--primary-color);
    color: var(--white);
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 181, 173, 0.2);
    text-decoration: none;
}

.btn-submit:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 181, 173, 0.3);
}

/* Branch Section */
.branch-section {
    background-color: var(--light-gray);
    padding: 60px 0;
    text-align: center;
    position: relative;
}

.branch-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #ddd, transparent);
}

.branch-section h2 {
    color: var(--text-color);
    font-size: 32px;
    margin-bottom: 20px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.branch-section h2:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.branch-section p {
    color: var(--text-light);
    font-size: 18px;
    margin-bottom: 30px;
}

.branch-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.branch-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-color);
    font-size: 16px;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    background-color: var(--white);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.branch-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.branch-item i {
    color: var(--primary-color);
    font-size: 20px;
}

.branch-social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
}

.branch-social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 18px;
    transition: var(--transition);
    text-decoration: none;
}

.branch-social-link.facebook {
    background-color: #1877f2;
}
.branch-social-link.twitter {
    background-color: #1da1f2;
}
.branch-social-link.instagram {
    background-color: #e4405f;
}
.branch-social-link.linkedin {
    background-color: #0077b5;
}

.branch-social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.whatsapp-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background-color: #25d366;
    color: var(--white);
    padding: 12px 30px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 18px;
    font-weight: 600;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.2);
    margin-top: 20px;
}

.whatsapp-btn i {
    font-size: 24px;
}

.whatsapp-btn:hover {
    background-color: #22c35e;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    color: var(--white);
}

/* Branch Map */
.branch-map {
    overflow: hidden;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.branch-map:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* Floating Chat Buttons */
.chat-btns {
    position: fixed;
    bottom: 30px;
    left: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
}

.chat-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 24px;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    position: relative;
}

.chat-btn::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    opacity: 0.5;
    z-index: -1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    70% {
        transform: scale(1.3);
        opacity: 0;
    }
    100% {
        transform: scale(1.3);
        opacity: 0;
    }
}

.chat-btn.whatsapp {
    background-color: #25d366;
}

.chat-btn.messenger {
    background-color: #0084ff;
}

.chat-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: var(--white);
}

/* Vision 2030 Logo */
.vision-2030 {
    text-align: center;
    padding: 40px 0;
    background-color: var(--white);
}

.vision-2030 img {
    max-width: 200px;
    height: auto;
    transition: var(--transition);
}

.vision-2030 img:hover {
    transform: scale(1.05);
}

/* All links */
a {
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 991px) {
    .hero-section {
        height: 500px;
    }

    .hero-content h1 {
        font-size: 52px;
    }

    .hero-content p {
        font-size: 20px;
        max-width: 600px;
    }

    .contact-form-wrapper {
        margin: 0 20px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 450px;
    }

    .hero-content h1 {
        font-size: 42px;
        margin-bottom: 20px;
    }

    .hero-content p {
        font-size: 18px;
        max-width: 500px;
    }

    .branch-section h2 {
        font-size: 28px;
    }

    .branch-info {
        gap: 20px;
    }

    .chat-btns {
        bottom: 20px;
        left: 20px;
    }

    .chat-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

@media (max-width: 576px) {
    .hero-section {
        height: 400px;
    }

    .hero-content {
        margin-top: -30px;
    }

    .hero-content h1 {
        font-size: 36px;
        margin-bottom: 15px;
    }

    .hero-content p {
        font-size: 16px;
        max-width: 100%;
        padding: 0 15px;
    }

    .contact-form-wrapper {
        padding: 30px 20px;
    }

    .whatsapp-btn {
        width: 100%;
        justify-content: center;
    }

    .social-links {
        gap: 10px;
    }

    .chat-btns {
        bottom: 15px;
        left: 15px;
    }

    .chat-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}
