<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Investment extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'featured_image',
        'investment_amount',
        'investment_category_id',
        'location',
        'tags',
        'investment_highlights',
        'contact_info',
        'is_published',
        'published_at'
    ];

    protected $casts = [
        'tags' => 'array',
        'investment_highlights' => 'array',
        'is_published' => 'boolean',
        'published_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($investment) {
            if (empty($investment->slug)) {
                $investment->slug = Str::slug($investment->title);
            }
        });
    }

    /**
     * Get the gallery images for the investment.
     */
    public function images()
    {
        return $this->hasMany(InvestmentImage::class)->orderBy('sort_order');
    }

    /**
     * Get the primary gallery image for the investment.
     */
    public function primaryImage()
    {
        return $this->hasOne(InvestmentImage::class)->where('is_primary', true);
    }

    public function category()
    {
        return $this->belongsTo(InvestmentCategory::class, 'investment_category_id');
    }
}
