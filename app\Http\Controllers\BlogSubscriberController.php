<?php

namespace App\Http\Controllers;

use App\Models\BlogSubscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BlogSubscriberController extends Controller
{
    /**
     * عرض نموذج الاشتراك في إشعارات المدونة
     *
     * @return \Illuminate\View\View
     */
    public function showSubscribeForm()
    {
        return view('blog.subscribe');
    }

    /**
     * طلب الإذن وتسجيل توكن FCM للمشترك
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function subscribe(Request $request)
    {
        try {
            $request->validate([
                'token' => 'required|string'
            ]);

            $token = $request->token;

            // تحقق من صحة التوكن
            if (empty($token) || $token === 'null' || $token === 'undefined' || strlen($token) < 10) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid token provided'
                ], 400);
            }

            // البحث عن مشترك موجود بنفس التوكن أو إنشاء واحد جديد
            $subscriber = BlogSubscriber::firstOrNew(['fcm_token' => $token]);

            // حفظ المشترك إذا كان جديدًا
            if (!$subscriber->exists) {
                $subscriber->save();

                Log::info('New blog subscriber registered', [
                    'id' => $subscriber->id,
                    'token_preview' => substr($token, 0, 10) . '...'
                ]);
            } else {
                Log::info('Existing subscriber token updated', [
                    'id' => $subscriber->id
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Successfully subscribed to blog notifications',
                'subscriber_id' => $subscriber->id
            ]);
        } catch (\Exception $e) {
            Log::error('Error registering blog subscriber', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error registering subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إلغاء الاشتراك من إشعارات المدونة
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unsubscribe(Request $request)
    {
        try {
            $request->validate([
                'token' => 'required|string'
            ]);

            $token = $request->token;

            // البحث عن المشترك وحذفه
            $subscriber = BlogSubscriber::where('fcm_token', $token)->first();

            if ($subscriber) {
                $subscriber->delete();

                Log::info('Blog subscriber unsubscribed', [
                    'id' => $subscriber->id
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Successfully unsubscribed from blog notifications'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Subscriber not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error unsubscribing blog subscriber', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error unsubscribing: ' . $e->getMessage()
            ], 500);
        }
    }
}
