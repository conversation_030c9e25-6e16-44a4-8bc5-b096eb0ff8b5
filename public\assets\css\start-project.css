:root {
    --primary-color: #00b5ad;
    --primary-dark: #008c85;
    --primary-light: #33c3bd;
    --secondary-color: #2c3e50;
    --text-color: #333;
    --text-light: #666;
    --white: #fff;
    --light-gray: #f8f9fa;
    --border-radius: 6px;
    --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
}

/* Hero Section */
.hero-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.5)),
        url("../img/start-your-project/sec_chemplnt82-1-1024x430.jpg");
    background-size: cover;
    background-position: center;
    height: 500px;
    display: flex;
    align-items: center;
    text-align: center;
    color: var(--white);
    position: relative;
    margin-bottom: 50px;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.hero-content p {
    font-size: 18px;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
}

.hero-btns {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 14px 32px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    font-size: 16px;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-outline {
    border: 2px solid var(--white);
    color: var(--white);
    padding: 14px 32px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    font-size: 16px;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-outline:hover {
    background-color: var(--white);
    color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Project Form Section */
.project-form-section {
    padding: 0 0 80px;
}

.form-wrapper {
    background-color: var(--white);
    padding: 40px 50px;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    position: relative;
    margin-top: -100px;
    z-index: 10;
    transition: var(--transition);
    border-top: none;
    overflow: hidden;
}

.form-wrapper:hover {
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.18);
    transform: translateY(-5px);
}

.form-wrapper::before {
    content: "";
    position: absolute;
    top: -5px;
    left: 0;
    height: 5px;
    width: 0;
    background: linear-gradient(
        to right,
        var(--primary-color),
        var(--primary-light)
    );
    transition: width 0.7s ease;
    display: none;
}

.form-wrapper:hover::before {
    width: 100%;
}

.form-wrapper h2 {
    color: var(--secondary-color);
    font-size: 36px;
    margin-bottom: 40px;
    font-weight: 700;
    text-align: center;
    position: relative;
}

.form-wrapper h2::after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(
        to right,
        var(--primary-color),
        var(--primary-light)
    );
    border-radius: 10px;
}

.project-form .form-group {
    margin-bottom: 25px;
    position: relative;
}

.project-form label {
    display: block;
    margin-bottom: 10px;
    color: var(--text-color);
    font-weight: 600;
    font-size: 16px;
    transition: var(--transition);
    padding-right: 5px;
    border-right: none;
}

.project-form .form-control {
    width: 100%;
    padding: 14px 18px;
    border: 1px solid #e6e6e6;
    border-radius: 10px;
    font-size: 15px;
    transition: var(--transition);
    background-color: #f9f9f9;
}

.project-form .form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 4px rgba(0, 181, 173, 0.1);
    background-color: var(--white);
}

.project-form .form-control:hover {
    border-color: #ccc;
}

.phone-input {
    position: relative;
}

.country-code {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 6px;
    color: var(--white);
    font-size: 14px;
    font-weight: 600;
    box-shadow: none;
}

.phone-input input {
    padding-left: 70px;
}

.btn-submit {
    background: linear-gradient(
        to right,
        var(--primary-color),
        var(--primary-light)
    );
    color: var(--white);
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 10px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 8px 20px rgba(0, 181, 173, 0.25);
    position: relative;
    overflow: hidden;
    z-index: 1;
    animation: pulse 2s infinite;
}

.btn-submit::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(
        to right,
        var(--primary-light),
        var(--primary-color)
    );
    transition: width 0.5s ease;
    z-index: -1;
}

.btn-submit:hover::after {
    width: 100%;
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 181, 173, 0.35);
}

.btn-submit:active {
    transform: translateY(0);
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300b5ad' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
    padding-right: 45px;
}

textarea.form-control {
    resize: none;
    min-height: 120px;
}

/* Form animations */
@keyframes formFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-group {
    animation: formFadeIn 0.5s ease-out forwards;
    opacity: 0;
}

.form-group:nth-child(1) {
    animation-delay: 0.1s;
}

.form-group:nth-child(2) {
    animation-delay: 0.2s;
}

.form-group:nth-child(3) {
    animation-delay: 0.3s;
}

.form-group:nth-child(4) {
    animation-delay: 0.4s;
}

.form-group:nth-child(5) {
    animation-delay: 0.5s;
}

.form-group:nth-child(6) {
    animation-delay: 0.6s;
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 80px 0;
    background-color: var(--light-gray);
    position: relative;
}

.why-choose-us h2 {
    text-align: center;
    color: var(--secondary-color);
    margin-bottom: 50px;
    font-size: 36px;
    font-weight: 700;
    position: relative;
}

.why-choose-us h2::after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.feature-card {
    background-color: var(--white);
    padding: 40px 30px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--box-shadow);
    height: 100%;
    transition: var(--transition);
    border-top: 4px solid var(--primary-color);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 100px;
    height: 100px;
    background-color: rgba(0, 181, 173, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    transition: var(--transition);
}

.feature-icon i {
    font-size: 40px;
    color: var(--primary-color);
    transition: var(--transition);
}

.feature-card:hover .feature-icon {
    background-color: var(--primary-color);
}

.feature-card:hover .feature-icon i {
    color: white;
}

.feature-card h4 {
    color: var(--secondary-color);
    font-size: 20px;
    margin-bottom: 15px;
    font-weight: 700;
}

.feature-card p {
    color: var(--text-light);
    font-size: 15px;
    line-height: 1.7;
    margin: 0;
}

/* Contact Section */
.contact-section {
    padding: 60px 0;
    background-color: var(--white);
}

.contact-info {
    background-color: var(--primary-color);
    padding: 40px;
    border-radius: var(--border-radius);
    color: var(--white);
    height: 100%;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
}

.contact-info h3 {
    font-size: 28px;
    margin-bottom: 30px;
    font-weight: 700;
    position: relative;
}

.contact-info h3::after {
    content: "";
    position: absolute;
    bottom: -10px;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: var(--white);
    border-radius: 2px;
}

.contact-info ul {
    list-style: none;
    padding: 0;
    margin: 0 0 30px;
}

.contact-info ul li {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    font-size: 16px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.contact-info ul li:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateX(-5px);
}

.contact-info ul li i {
    font-size: 20px;
    width: 30px;
    text-align: center;
}

.project-social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
}

.project-social-links a {
    width: 45px;
    height: 45px;
    border: 2px solid var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 18px;
    transition: var(--transition);
    text-decoration: none;
}

.project-social-links a:hover {
    background-color: var(--white);
    color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-map {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    height: 450px;
    width: 100%;
}

.contact-map iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Responsive Design */
@media (max-width: 991px) {
    .hero-content h1 {
        font-size: 42px;
    }

    .form-wrapper h2 {
        font-size: 28px;
    }

    .why-choose-us h2 {
        font-size: 28px;
    }

    .feature-card {
        padding: 30px 20px;
        margin-bottom: 30px;
    }

    .feature-icon {
        width: 70px;
        height: 70px;
    }

    .feature-card h4 {
        font-size: 18px;
    }

    .contact-info {
        margin-bottom: 30px;
    }

    .contact-map {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 400px;
    }

    .hero-content h1 {
        font-size: 36px;
    }

    .hero-content p {
        font-size: 16px;
    }

    .form-wrapper {
        padding: 30px;
        margin-top: -80px;
    }

    .hero-btns {
        flex-direction: column;
        align-items: center;
    }

    .hero-btns .btn {
        width: 100%;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
    }

    .feature-icon img {
        width: 30px;
        height: 30px;
    }

    .contact-info,
    .contact-map {
        width: 100%;
    }

    .contact-map {
        height: 350px;
    }

    .contact-section .row {
        flex-direction: column;
    }

    .contact-section .col-lg-6 {
        width: 100%;
        flex-basis: 100%;
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .hero-section {
        height: 350px;
    }

    .hero-content h1 {
        font-size: 30px;
    }

    .hero-content p {
        font-size: 14px;
    }

    .form-wrapper {
        padding: 20px;
        margin-top: -60px;
    }

    .form-wrapper h2 {
        font-size: 24px;
    }

    .project-form label {
        font-size: 14px;
    }

    .why-choose-us h2 {
        font-size: 24px;
    }

    .feature-card h4 {
        font-size: 16px;
    }

    .contact-info h3 {
        font-size: 24px;
    }

    .contact-info ul li {
        font-size: 14px;
        padding: 12px;
    }

    .contact-map {
        height: 300px;
    }

    .project-social-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .project-social-links a {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Additional mobile fixes */
@media (max-width: 480px) {
    .hero-section {
        height: 300px;
    }

    .hero-content h1 {
        font-size: 26px;
    }

    .form-wrapper {
        margin-top: -50px;
        padding: 15px;
    }

    .btn-submit {
        padding: 12px;
        font-size: 15px;
    }

    .contact-info {
        padding: 25px;
    }

    .contact-info ul li {
        gap: 10px;
        margin-bottom: 15px;
        font-size: 13px;
    }

    .contact-map {
        height: 250px;
    }
}

/* Form group icon styling */
.project-form label i {
    color: var(--primary-color);
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* Vision 2030 Logo */
.vision-2030 {
    text-align: center;
    padding: 40px 0;
    background-color: var(--white);
}

.vision-2030 img {
    max-width: 220px;
    height: auto;
    transition: var(--transition);
    filter: grayscale(0.1);
}

.vision-2030 img:hover {
    transform: scale(1.05);
    filter: grayscale(0);
}

/* Input Field Highlight Effect */
.project-form .form-control::placeholder {
    color: #aaa;
    font-style: italic;
    font-size: 14px;
}

/* Success message styling */
.toast-message {
    border-right: 4px solid #28a745;
    animation: slideIn 0.5s forwards;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Add subtle animation to the button */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 181, 173, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(0, 181, 173, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 181, 173, 0);
    }
}
