<script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>

<script>
    (function() {
        if (window.visitorFcmInitialized) return;
        window.visitorFcmInitialized = true;

        const firebaseConfig = {
            apiKey: "{{ config('services.firebase.api_key') }}",
            authDomain: "{{ config('services.firebase.auth_domain') }}",
            projectId: "{{ config('services.firebase.project_id') }}",
            storageBucket: "{{ config('services.firebase.storage_bucket') }}",
            messagingSenderId: "{{ config('services.firebase.messaging_sender_id') }}",
            appId: "{{ config('services.firebase.app_id') }}",
            vapidKey: "{{ config('services.firebase.vapid_key') }}"
        };

        // توليد معرف فريد للجهاز وتخزينه
        function generateDeviceId() {
            // تحقق إذا كان هناك معرف موجود بالفعل
            let deviceId = localStorage.getItem('visitor_device_id');

            if (!deviceId) {
                // إنشاء معرف فريد باستخدام تاريخ ووقت حالي وعشوائي
                const timestamp = new Date().getTime();
                const randomPart = Math.floor(Math.random() * 1000000);
                deviceId = `device_${timestamp}_${randomPart}`;

                // تخزين المعرف في التخزين المحلي
                localStorage.setItem('visitor_device_id', deviceId);
            }

            return deviceId;
        }

        const deviceId = generateDeviceId();

        // استكشاف معلومات المتصفح ونظام التشغيل
        function getBrowserInfo() {
            const userAgent = navigator.userAgent;
            let browser = "Unknown";
            let os = "Unknown";

            // تحديد المتصفح
            if (userAgent.indexOf("Edge") > -1) browser = "Edge";
            else if (userAgent.indexOf("Edg") > -1) browser = "Edge";
            else if (userAgent.indexOf("Chrome") > -1) browser = "Chrome";
            else if (userAgent.indexOf("Firefox") > -1) browser = "Firefox";
            else if (userAgent.indexOf("Safari") > -1) browser = "Safari";
            else if (userAgent.indexOf("MSIE") > -1 || userAgent.indexOf("Trident") > -1) browser = "Internet Explorer";
            else if (userAgent.indexOf("Opera") > -1 || userAgent.indexOf("OPR") > -1) browser = "Opera";

            // تحديد نظام التشغيل
            if (userAgent.indexOf("Windows") > -1) os = "Windows";
            else if (userAgent.indexOf("Mac") > -1) os = "MacOS";
            else if (userAgent.indexOf("Linux") > -1) os = "Linux";
            else if (userAgent.indexOf("Android") > -1) os = "Android";
            else if (userAgent.indexOf("iOS") > -1 || /iPhone|iPad|iPod/i.test(userAgent)) os = "iOS";

            return { browser, os };
        }

        const browserInfo = getBrowserInfo();

        let firebaseApp;
        try {
            firebaseApp = firebase.app();
        } catch (e) {
            firebaseApp = firebase.initializeApp(firebaseConfig);
        }

        const messaging = firebase.messaging();

        const forceTokenRefresh = true;

        async function resetStoredToken() {
            const oldToken = localStorage.getItem('visitor_fcm_token');

            if (oldToken) {
                try {
                    await messaging.deleteToken();
                } catch (e) {
                    // Error handling silently
                }
            }

            localStorage.removeItem('visitor_fcm_token');
            localStorage.removeItem('visitor_fcm_token_last_sent');
        }

        let currentFcmToken = localStorage.getItem('visitor_fcm_token');
        let isInvalidToken = !currentFcmToken ||
            currentFcmToken === 'null' ||
            currentFcmToken === 'undefined' ||
            currentFcmToken.length < 10;

        if (forceTokenRefresh || isInvalidToken) {
            resetStoredToken();
            currentFcmToken = '';
        }

        let tokenLastSent = parseInt(localStorage.getItem('visitor_fcm_token_last_sent') || '0');
        const isNewSession = false;

        const TOKEN_UPDATE_INTERVAL = 24 * 60 * 60 * 1000; // 24 ساعة
        const isEdgeBrowser = navigator.userAgent.indexOf("Edg") !== -1;

        // إضافة عنصر عام لتخزين حالة الاشتراك
        window.notificationSubscriptionStatus = {
            isSubscribed: localStorage.getItem('visitor_fcm_token') ? true : false,
            permissionGranted: Notification.permission === 'granted'
        };

        async function initializeVisitorMessaging() {
            try {
                // Check if user has explicitly unsubscribed before
                const explicitlyUnsubscribed = localStorage.getItem('visitor_explicitly_unsubscribed') === 'true';

                const permission = await Notification.requestPermission();
                if (permission !== 'granted') {
                    window.notificationSubscriptionStatus.permissionGranted = false;
                    return;
                }

                window.notificationSubscriptionStatus.permissionGranted = true;

                const swOptions = {
                    updateViaCache: 'none'
                };

                const registration = await navigator.serviceWorker.register('/admin/firebase-messaging-sw.js', swOptions);

                if (isEdgeBrowser) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                messaging.useServiceWorker(registration);

                if (registration.active) {
                    registration.active.postMessage({
                        type: 'FIREBASE_CONFIG',
                        config: firebaseConfig
                    });
                }

                const now = Date.now();
                let shouldUpdateServer = false;

                // Skip token retrieval if user explicitly unsubscribed
                if (explicitlyUnsubscribed) {
                    currentFcmToken = null;
                    window.notificationSubscriptionStatus.isSubscribed = false;
                    updateNotificationToggleButton();
                    return;
                }

                try {
                    const tokenFromFirebase = await messaging.getToken();

                    if (tokenFromFirebase) {
                        currentFcmToken = tokenFromFirebase;
                        shouldUpdateServer = true;
                        localStorage.setItem('visitor_fcm_token', currentFcmToken);
                        window.notificationSubscriptionStatus.isSubscribed = true;
                    } else {
                        currentFcmToken = null;
                        localStorage.removeItem('visitor_fcm_token');
                        window.notificationSubscriptionStatus.isSubscribed = false;
                    }
                } catch (error) {
                    shouldUpdateServer = false;
                    currentFcmToken = null;
                    localStorage.removeItem('visitor_fcm_token');
                    window.notificationSubscriptionStatus.isSubscribed = false;
                }

                const isValidFinalToken = currentFcmToken &&
                    currentFcmToken !== 'null' &&
                    currentFcmToken !== 'undefined' &&
                    currentFcmToken.length > 10;

                if (shouldUpdateServer && isValidFinalToken) {
                    const updateResult = await updateTokenOnServer(currentFcmToken);
                    if (updateResult.success) {
                        localStorage.setItem('visitor_fcm_token_last_sent', now.toString());
                        window.notificationSubscriptionStatus.isSubscribed = true;
                    }
                } else if (shouldUpdateServer) {
                    localStorage.removeItem('visitor_fcm_token');
                    window.notificationSubscriptionStatus.isSubscribed = false;
                }

                messaging.onTokenRefresh(async () => {
                    try {
                        await resetStoredToken();

                        const refreshedToken = await messaging.getToken();

                        if (refreshedToken && refreshedToken !== 'null' && refreshedToken !== 'undefined') {
                            currentFcmToken = refreshedToken;
                            localStorage.setItem('visitor_fcm_token', refreshedToken);
                            localStorage.setItem('visitor_fcm_token_last_sent', Date.now().toString());
                            window.notificationSubscriptionStatus.isSubscribed = true;

                            const updateResult = await updateTokenOnServer(refreshedToken);
                            if (!updateResult.success) {
                                localStorage.removeItem('visitor_fcm_token');
                                window.notificationSubscriptionStatus.isSubscribed = false;
                            }
                        } else {
                            localStorage.removeItem('visitor_fcm_token');
                            window.notificationSubscriptionStatus.isSubscribed = false;
                        }
                    } catch (error) {
                        localStorage.removeItem('visitor_fcm_token');
                        window.notificationSubscriptionStatus.isSubscribed = false;
                    }
                });

                // تحديث حالة زر الإشعارات
                updateNotificationToggleButton();
            } catch (error) {
                localStorage.removeItem('visitor_fcm_token');
                window.notificationSubscriptionStatus.isSubscribed = false;
            }
        }

        async function updateTokenOnServer(token) {
            if (!token || token === 'null' || token === 'undefined' || token.length < 10) {
                return {
                    success: false,
                    error: 'Invalid token'
                };
            }

            try {
                const response = await fetch('{{ route("visitor.fcm.token.update") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        token,
                        device_id: deviceId,
                        browser: browserInfo.browser,
                        os: browserInfo.os
                    })
                });

                const data = await response.json();
                return data;
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        messaging.onMessage((payload) => {
            // عرض الإشعار في المتصفح حتى عندما يكون المستخدم يتصفح الموقع
            if (payload.notification) {
                const notificationOptions = {
                    body: payload.notification.body,
                    icon: '/favicon.ico',
                    badge: '/favicon.ico',
                    vibrate: [200, 100, 200, 100, 200], // اهتزاز أقوى
                    requireInteraction: true,
                    tag: 'notification-' + Date.now(), // تغيير الـ tag لكل إشعار لضمان ظهور جميع الإشعارات
                    renotify: true, // إعادة تنبيه المستخدم حتى لو كان الـ tag مكرر
                    actions: [
                        {
                            action: 'open',
                            title: 'عرض المحتوى'
                        }
                    ]
                };

                // تسريع عرض الإشعارات بإضافة طبقة إضافية للتنفيذ الفوري
                setTimeout(() => {
                    try {
                        // عرض إشعار قابل للنقر
                        const notification = new Notification(payload.notification.title, notificationOptions);

                        // عند النقر على الإشعار
                        notification.onclick = function() {
                            window.focus();
                            notification.close();
                            // يمكنك إضافة سلوك إضافي هنا، مثل التنقل إلى صفحة معينة
                            if (payload.data && payload.data.link) {
                                window.location.href = payload.data.link;
                            }
                        };
                    } catch (e) {
                        // Error handled silently
                    }
                }, 0);
            }
        });

        // دالة لإلغاء الاشتراك في الإشعارات
        window.unsubscribeFromNotifications = async function() {
            try {
                const deviceId = localStorage.getItem('visitor_device_id');
                if (deviceId) {
                    const response = await fetch('{{ route("visitor.fcm.unsubscribe") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            device_id: deviceId
                        })
                    });

                    const result = await response.json();
                    if (result.success) {
                        await messaging.deleteToken();
                        localStorage.removeItem('visitor_fcm_token');
                        // Set explicit unsubscribe flag
                        localStorage.setItem('visitor_explicitly_unsubscribed', 'true');
                        window.notificationSubscriptionStatus.isSubscribed = false;
                        updateNotificationToggleButton();
                        showToast('تم إيقاف الإشعارات', 'info');
                        return true;
                    }
                }

                return false;
            } catch (error) {
                console.error('Error unsubscribing from notifications:', error);
                showToast('حدث خطأ أثناء إيقاف الإشعارات', 'error');
                return false;
            }
        };

        // دالة للاشتراك في الإشعارات
        window.subscribeToNotifications = async function() {
            try {
                const permission = await Notification.requestPermission();
                if (permission !== 'granted') {
                    showToast('يرجى السماح بالإشعارات من إعدادات المتصفح', 'warning');
                    return false;
                }

                // Remove explicit unsubscribe flag when user subscribes
                localStorage.removeItem('visitor_explicitly_unsubscribed');

                const token = await messaging.getToken();
                if (token) {
                    localStorage.setItem('visitor_fcm_token', token);
                    const result = await updateTokenOnServer(token);

                    if (result.success) {
                        window.notificationSubscriptionStatus.isSubscribed = true;
                        localStorage.setItem('visitor_fcm_token_last_sent', Date.now().toString());
                        updateNotificationToggleButton();
                        showToast('تم تفعيل الإشعارات بنجاح', 'success');
                        return true;
                    }
                }

                return false;
            } catch (error) {
                console.error('Error subscribing to notifications:', error);
                showToast('حدث خطأ أثناء تفعيل الإشعارات', 'error');
                return false;
            }
        };

        // دالة للتبديل بين الاشتراك وإلغاء الاشتراك
        window.toggleNotifications = async function() {
            if (window.notificationSubscriptionStatus.isSubscribed) {
                return await window.unsubscribeFromNotifications();
            } else {
                return await window.subscribeToNotifications();
            }
        };

        // عرض رسالة منبثقة (Toast)
        function showToast(message, type = 'info') {
            // التحقق من وجود عنصر التوست الحاوي
            let toastContainer = document.getElementById('notifications-toast-container');

            // إنشاء عنصر حاوي للتوست إذا لم يكن موجودًا
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'notifications-toast-container';
                toastContainer.className = 'notifications-toast-container';
                document.body.appendChild(toastContainer);
            }

            // إنشاء عنصر التوست
            const toast = document.createElement('div');
            toast.className = `notifications-toast ${type}`;

            // إنشاء المحتوى
            toast.innerHTML = `
                <div class="notifications-toast-content">
                    <span class="notifications-toast-icon">
                        ${type === 'success' ? '<i class="fas fa-check-circle"></i>' :
                         type === 'error' ? '<i class="fas fa-exclamation-circle"></i>' :
                         type === 'warning' ? '<i class="fas fa-exclamation-triangle"></i>' :
                         '<i class="fas fa-info-circle"></i>'}
                    </span>
                    <span class="notifications-toast-message">${message}</span>
                    <span class="notifications-toast-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
            `;

            // إضافة التوست للحاوي
            toastContainer.appendChild(toast);

            // إزالة التوست بعد 3 ثوان
            setTimeout(() => {
                if (toast && toast.parentElement) {
                    toast.classList.add('fade-out');
                    setTimeout(() => {
                        if (toast && toast.parentElement) {
                            toast.remove();
                        }
                    }, 300);
                }
            }, 3000);
        }

        // تحديث حالة زر التبديل
        window.updateNotificationToggleButton = function() {
            // تحديث زر الإشعارات في الشريط العلوي
            const topBarButtons = document.querySelectorAll('.notification-toggle-btn');
            topBarButtons.forEach(button => {
                if (window.notificationSubscriptionStatus.isSubscribed) {
                    button.classList.add('active');
                    button.setAttribute('title', 'إيقاف الإشعارات');
                } else {
                    button.classList.remove('active');
                    button.setAttribute('title', 'تفعيل الإشعارات');
                }
            });

            // تحديث زر الإشعارات في الفوتر
            const footerButtons = document.querySelectorAll('.chat-btn.notification');
            footerButtons.forEach(button => {
                if (window.notificationSubscriptionStatus.isSubscribed) {
                    button.classList.add('active');
                    button.setAttribute('title', 'إيقاف الإشعارات');
                } else {
                    button.classList.remove('active');
                    button.setAttribute('title', 'تفعيل الإشعارات');
                }
            });
        }

        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            initializeVisitorMessaging();
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                initializeVisitorMessaging();
            }, {
                once: true
            });
        }
    })();
</script>
