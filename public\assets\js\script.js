// Main JavaScript file for the site

document.addEventListener('DOMContentLoaded', function() {
    // Process meta tags to handle company name correctly
    function processMetaTags() {
        // Find meta tags with HTML content (spans)
        document.querySelectorAll('meta').forEach(function(meta) {
            if (meta.getAttribute('content') && meta.getAttribute('content').includes('<span')) {
                // Extract content
                let content = meta.getAttribute('content');

                // Create a temporary div to parse HTML
                let tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;

                // Replace spans with plain text - using "Khobara" for notranslate spans
                tempDiv.querySelectorAll('span.notranslate').forEach(function(span) {
                    span.replaceWith('Khobara');
                });

                // Update meta content with processed text
                meta.setAttribute('content', tempDiv.textContent);
            }
        });

        // Fix document title
        if (document.title.includes('<span')) {
            let tempDiv = document.createElement('div');
            tempDiv.innerHTML = document.title;

            tempDiv.querySelectorAll('span.notranslate').forEach(function(span) {
                span.replaceWith('Khobara');
            });

            document.title = tempDiv.textContent;
        }
    }

    // Call immediately to process meta tags
    processMetaTags();

    // Remove the custom navbar toggle handler - Bootstrap handles this automatically

    // Fix question marks in titles for LTR languages
    function fixQuestionMarks() {
        if (document.documentElement.dir === 'ltr') {
            // Get all section titles
            const sectionTitles = document.querySelectorAll('.section-title');
            sectionTitles.forEach(title => {
                // Find the title that contains "choose" or "Choose" when translated
                if (title.textContent.includes('choose') ||
                    title.textContent.includes('Choose') ||
                    title.textContent.includes('why') ||
                    title.textContent.includes('Why')) {

                    title.setAttribute('data-has-question-mark', 'true');

                    // Remove question mark from beginning if exists
                    if (title.textContent.trim().startsWith('?')) {
                        title.textContent = title.textContent.replace(/^\s*\?\s*/, '');
                    }
                }
            });
        }
    }

    // Update meta tags when page language changes
    function updateMetaTags() {
        // Check if page is in LTR mode (non-Arabic)
        const isLTR = document.documentElement.dir === 'ltr';

        // Find all meta tags with company name data attributes
        document.querySelectorAll('meta[data-ar-company], meta[data-en-company]').forEach(function(meta) {
            // Get the appropriate company name based on direction
            const arCompany = meta.getAttribute('data-ar-company');
            const enCompany = meta.getAttribute('data-en-company');

            if (!arCompany || !enCompany) return;

            // Get current content
            let content = meta.getAttribute('content');

            // Save original content if not already saved
            if (!meta.getAttribute('data-original-content') && !isLTR) {
                meta.setAttribute('data-original-content', content);
            }

            // Restore from original or replace with new content
            if (isLTR) {
                // Replace Arabic company name with English version
                if (content && arCompany) {
                    meta.setAttribute('content', content.replace(arCompany, enCompany));
                }
            } else {
                // Restore original content if available
                const originalContent = meta.getAttribute('data-original-content');
                if (originalContent) {
                    meta.setAttribute('content', originalContent);
                }
            }
        });
    }

    // Watch for changes in HTML dir attribute (when Google Translate is used)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'dir') {
                fixQuestionMarks();
                removeGoogleTranslateHighlights();
                updateMetaTags();
            }
        });
    });

    // Start observing the HTML element for dir attribute changes
    observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['dir']
    });

    // Initial calls
    fixQuestionMarks();
    updateMetaTags();

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                window.scrollTo({
                    top: target.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    });

    // WhatsApp button functionality
    const whatsappLinks = document.querySelectorAll('.chat-btn.whatsapp, .btn-whatsapp');
    whatsappLinks.forEach(link => {
        if (!link.getAttribute('href') || link.getAttribute('href') === '#') {
            link.setAttribute('href', 'https://wa.me/966569617288');
        }
    });

    // Fix Google Translate highlight selection
    function removeGoogleTranslateHighlights() {
        // Remove Google Translate highlights
        const highlightElements = document.querySelectorAll('.goog-text-highlight, [style*="background-color: rgb(204, 232, 254)"]');
        highlightElements.forEach(el => {
            el.style.backgroundColor = 'transparent';
            el.style.boxShadow = 'none';
            el.style.border = 'none';

            // Remove the highlight class
            if (el.classList.contains('goog-text-highlight')) {
                el.classList.remove('goog-text-highlight');
            }
        });

        // Also look for inline style attributes with background color
        document.querySelectorAll('*[style*="background-color:"]').forEach(el => {
            if (el.style.backgroundColor === 'rgb(204, 232, 254)') {
                el.style.backgroundColor = 'transparent';
                el.style.boxShadow = 'none';
                el.style.border = 'none';
            }
        });
    }

    // Run initial cleanup
    removeGoogleTranslateHighlights();

    // Add mutation observer to detect when Google Translate adds highlights
    const highlightObserver = new MutationObserver(() => {
        removeGoogleTranslateHighlights();
    });

    // Start observing the document for added nodes
    highlightObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });

    // Add event listener for mouseover to prevent highlight
    document.addEventListener('mouseover', function(e) {
        // If target has Google Translate highlight, remove it
        if (e.target.classList.contains('goog-text-highlight') ||
            (e.target.style && e.target.style.backgroundColor === 'rgb(204, 232, 254)')) {
            e.target.style.backgroundColor = 'transparent';
            e.target.style.boxShadow = 'none';
            e.target.style.border = 'none';
            e.target.classList.remove('goog-text-highlight');
        }
    }, true);

    // Function to display "KHOBARA" when translated
    function updateCompanyName() {
        // Check if page is in LTR mode (non-Arabic)
        if (document.documentElement.dir === 'ltr') {
            // Find all company name elements
            document.querySelectorAll('.company-name').forEach(function(el) {
                // Get alternative text
                const altText = el.getAttribute('data-alt-text');

                // Save original text if not already saved
                if (!el.getAttribute('data-original-text')) {
                    el.setAttribute('data-original-text', el.textContent);
                }

                // Display alternative text
                if (altText) {
                    el.textContent = altText;
                }
            });
        } else {
            // If RTL (Arabic), restore original text
            document.querySelectorAll('.company-name').forEach(function(el) {
                const originalText = el.getAttribute('data-original-text');
                if (originalText) {
                    el.textContent = originalText;
                }
            });
        }
    }

    // Initial call
    updateCompanyName();

    // Watch for changes in direction (when page is translated)
    const dirObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'dir') {
                updateCompanyName();
            }
        });
    });

    // Start observing direction changes
    dirObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['dir']
    });
});
