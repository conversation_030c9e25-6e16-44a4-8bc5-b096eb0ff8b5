<?php

namespace App\Mail;

use Illuminate\Mail\Mailable;

class InvestmentOpportunity extends Mailable
{

    /**
     * بيانات الفرصة الاستثمارية
     *
     * @var array
     */
    public $data;

    /**
     * إنشاء مثيل جديد من الرسالة
     *
     * @param  array  $data
     * @return void
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * بناء الرسالة
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('فرصة استثمارية: ' . $this->data['subject'])
            ->view('emails.investment-opportunity')
            ->with([
                'subject' => $this->data['subject'],
                'investment_type' => $this->data['investment_type'],
                'investment_amount' => $this->data['investment_amount'],
                'location' => $this->data['location'],
                'description' => $this->data['description'],
                'highlights' => $this->data['highlights'],
                'contact_info_array' => $this->data['contact_info_array'],
                'recipient_email' => $this->data['recipient_email']
            ]);
    }
}
