<?php

namespace App\Services;

use App\Models\VisitorToken;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class VisitorNotificationService
{
    private $firebaseService;

    public function __construct()
    {
        $this->firebaseService = new FirebaseNotificationService();
    }

    /**
     * إرسال إشعار لجميع الزوار النشطين
     *
     * @param string $title عنوان الإشعار
     * @param string $body نص الإشعار
     * @param string $link الرابط المستهدف عند النقر على الإشعار
     * @param string|null $topic موضوع معين للتصفية (اختياري)
     * @return array
     */
    public function sendToAllVisitors(string $title, string $body, string $link = '/', string $topic = null)
    {
        try {
            Log::info('Starting to send notifications to all visitors', [
                'title' => $title,
                'topic' => $topic
            ]);

            $query = VisitorToken::whereNotNull('fcm_token')
                ->where('is_active', true)
                ->where('is_subscribed', true)
                ->where('fcm_token', '!=', 'null');

            // تصفية حسب الموضوع إذا تم تحديده
            if ($topic) {
                $query->where(function($q) use ($topic) {
                    $q->where('topic', $topic)
                      ->orWhereNull('topic'); // يشمل أيضًا السجلات بدون موضوع محدد
                });
            }

            // تحسين الأداء: تنفيذ الاستعلام بشكل أكثر كفاءة
            $visitors = $query->select(['id', 'fcm_token', 'device_id'])->get();

            Log::info('Found visitors to notify', [
                'count' => $visitors->count(),
                'topic' => $topic
            ]);

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            // تحسين: استخدام الإرسال المتوازي لتسريع العملية
            $sendPromises = [];

            foreach ($visitors as $visitor) {
                try {
                    if (empty($visitor->fcm_token) || $visitor->fcm_token === 'null' || strlen($visitor->fcm_token) < 10) {
                        Log::info('Skipping visitor with invalid token', [
                            'visitor_id' => $visitor->id
                        ]);

                        // تحديث السجل لإيقاف تنشيطه
                        $visitor->is_active = false;
                        $visitor->save();

                        $results[$visitor->id] = [
                            'success' => false,
                            'error' => 'Invalid token'
                        ];
                        $failureCount++;
                        continue;
                    }

                    // تحسين البيانات المرسلة للإشعار
                    $notificationData = [
                        'priority' => 'high', // أولوية عالية
                        'time_to_live' => 0, // إرسال فوري وعدم الانتظار
                        'direct_boot_ok' => true, // السماح بالإرسال حتى في وضع direct boot
                    ];

                    // تخزين معرف الجهاز في البيانات لمزيد من المعلومات
                    $dataPayload = [
                        'link' => $link,
                        'device_id' => $visitor->device_id,
                        'notification_id' => uniqid('notify_', true),
                        'timestamp' => time()
                    ];

                    $result = $this->firebaseService->sendNotification(
                        $visitor->fcm_token,
                        $title,
                        $body,
                        $link,
                        $notificationData,
                        $dataPayload
                    );

                    if (!$result['success'] && isset($result['message']['error'])) {
                        if (
                            isset($result['message']['error']['message']) &&
                            (strpos($result['message']['error']['message'], 'The registration token is not a valid') !== false ||
                            strpos($result['message']['error']['message'], 'expired') !== false)
                        ) {
                            Log::warning('Visitor token is invalid, marking as inactive', [
                                'visitor_id' => $visitor->id
                            ]);

                            $visitor->is_active = false;
                            $visitor->save();
                        }
                        $failureCount++;
                    } else {
                        $successCount++;
                    }

                    $results[$visitor->id] = $result;

                } catch (\Exception $e) {
                    Log::error('Error sending notification to visitor', [
                        'visitor_id' => $visitor->id,
                        'error' => $e->getMessage()
                    ]);

                    $results[$visitor->id] = [
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    $failureCount++;
                }
            }

            return [
                'success' => true,
                'total' => $visitors->count(),
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'results' => $results
            ];

        } catch (\Exception $e) {
            Log::error('Error sending notifications to visitors', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * إرسال إشعار لزائر محدد بواسطة معرف الجهاز
     *
     * @param string $deviceId معرف الجهاز
     * @param string $title عنوان الإشعار
     * @param string $body نص الإشعار
     * @param string $link الرابط المستهدف
     * @return array
     */
    public function sendToVisitor(string $deviceId, string $title, string $body, string $link = '/')
    {
        try {
            $visitor = VisitorToken::where('device_id', $deviceId)
                ->where('is_active', true)
                ->where('is_subscribed', true)
                ->first();

            if (!$visitor || empty($visitor->fcm_token)) {
                Log::warning('Visitor not found or has no valid token', [
                    'device_id' => $deviceId
                ]);

                return [
                    'success' => false,
                    'error' => 'Visitor not found or has no valid token'
                ];
            }

            return $this->firebaseService->sendNotification(
                $visitor->fcm_token,
                $title,
                $body,
                $link
            );

        } catch (\Exception $e) {
            Log::error('Error sending notification to specific visitor', [
                'device_id' => $deviceId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
